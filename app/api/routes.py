"""
FastAPI routes for OBD2 diagnostic system
"""
import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse

from .models import *
from .frontend_routes import frontend_router
from .enhanced_routes import enhanced_router
from ..obd_interface.obd_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>TCResult, OBDParameter
from ..obd_interface.can_reader import CANReader, ECUInfo
from ..obd_interface.dtc_parser import DTCParser
from ..obd_interface.vehicle_detector import VehicleInfo
from ..ai_engine.explain_dtc import DTCExplainer
from ..ai_engine.prompt_builder import DiagnosticContext, VehicleContext
from ..ai_engine.gemini_integration import gemini_integration
from ..brand_profiles.toyota import ToyotaProfile
from ..brand_profiles.vag import VAGProfile
from ..brand_profiles.bmw import BMWProfile
from ..reference_integration.database_migrator import database_migrator
from ..obd_interface.connection_manager import connection_manager
from ..obd_interface.safety_manager import safety_manager
from ..live_data.enhanced_monitor import EnhancedLiveMonitor, SensorType


logger = logging.getLogger(__name__)


# JSON Encoder for datetime serialization
class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle datetime objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def safe_json_response(data: Any, status_code: int = 200) -> JSONResponse:
    """Create a JSON response with proper datetime serialization"""
    try:
        json_str = json.dumps(data, cls=DateTimeEncoder, ensure_ascii=False)
        return JSONResponse(content=json.loads(json_str), status_code=status_code)
    except Exception as e:
        logger.error(f"JSON serialization error: {e}")
        # Fallback to string representation
        fallback_data = str(data) if not isinstance(data, dict) else {
            k: str(v) if isinstance(v, datetime) else v
            for k, v in data.items()
        }
        return JSONResponse(content=fallback_data, status_code=status_code)


# Global instances
obd_reader = OBDReader()
can_reader = CANReader()
dtc_parser = DTCParser()
dtc_explainer = DTCExplainer()

# Brand profiles
brand_profiles = {
    "toyota": ToyotaProfile(),
    "lexus": ToyotaProfile(),
    "volkswagen": VAGProfile(),
    "audi": VAGProfile(),
    "skoda": VAGProfile(),
    "seat": VAGProfile(),
    "bmw": BMWProfile(),
    "mini": BMWProfile()
}

# Create router
router = APIRouter()


# Connection Management
@router.post("/connect", response_model=ConnectionResponse)
async def connect_obd(request: ConnectionRequest):
    """
    Connect to OBD2 adapter
    """
    try:
        if request.connection_type == ConnectionType.USB or request.connection_type == ConnectionType.BLUETOOTH:
            # Configure OBD reader
            obd_reader.port = request.port
            obd_reader.baudrate = request.baudrate or 38400
            
            # Attempt connection
            success = await obd_reader.connect()
            
            if success:
                vehicle_info = await obd_reader.get_vehicle_info()
                return ConnectionResponse(
                    connected=True,
                    connection_type=request.connection_type,
                    port=obd_reader.connection.port_name() if obd_reader.connection else request.port,
                    protocol=vehicle_info.get("protocol"),
                    supported_commands=vehicle_info.get("supported_commands"),
                    vehicle_info=vehicle_info
                )
            else:
                return ConnectionResponse(
                    connected=False,
                    error_message="Failed to connect to OBD2 adapter"
                )
        
        elif request.connection_type == ConnectionType.CAN:
            # Configure CAN reader
            can_reader.interface = request.port or "can0"
            
            # Attempt connection
            success = await can_reader.connect()
            
            if success:
                return ConnectionResponse(
                    connected=True,
                    connection_type=request.connection_type,
                    port=can_reader.interface
                )
            else:
                return ConnectionResponse(
                    connected=False,
                    error_message="Failed to connect to CAN interface"
                )
        
        else:
            raise HTTPException(status_code=400, detail="Unsupported connection type")
            
    except Exception as e:
        logger.error(f"Connection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/disconnect")
async def disconnect_obd():
    """
    Disconnect from OBD2 adapter
    """
    try:
        await obd_reader.disconnect()
        await can_reader.disconnect()
        return {"message": "Disconnected successfully"}
    except Exception as e:
        logger.error(f"Disconnection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection/status", response_model=ConnectionResponse)
async def get_connection_status():
    """
    Get current connection status
    """
    try:
        obd_connected = obd_reader.is_connected
        can_connected = can_reader.is_connected
        
        if obd_connected:
            vehicle_info = await obd_reader.get_vehicle_info()
            return ConnectionResponse(
                connected=True,
                connection_type=ConnectionType.USB,  # or BLUETOOTH
                port=obd_reader.connection.port_name() if obd_reader.connection else None,
                protocol=vehicle_info.get("protocol"),
                supported_commands=vehicle_info.get("supported_commands"),
                vehicle_info=vehicle_info
            )
        elif can_connected:
            return ConnectionResponse(
                connected=True,
                connection_type=ConnectionType.CAN,
                port=can_reader.interface
            )
        else:
            return ConnectionResponse(connected=False)
            
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection/ports")
async def list_available_ports():
    """
    List available serial ports
    """
    try:
        ports = OBDReader.list_available_ports()
        return {"ports": ports}
    except Exception as e:
        logger.error(f"Port listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Diagnostic Operations
@router.post("/scan", response_model=DiagnosticScanResponse)
async def perform_diagnostic_scan(request: DiagnosticRequest):
    """
    Perform comprehensive diagnostic scan
    """
    try:
        start_time = datetime.now()
        dtcs = []
        parameters = []
        
        # Check connection
        if not obd_reader.is_connected and not can_reader.is_connected:
            raise HTTPException(status_code=400, detail="No OBD connection available")
        
        # Read DTCs
        if request.include_dtcs:
            if obd_reader.is_connected:
                obd_dtcs = await obd_reader.read_dtcs()
                dtcs.extend([
                    DTCResponse(
                        code=dtc.code,
                        description=dtc.description,
                        status=dtc.status,
                        severity=DTCSeverity.UNKNOWN,  # Will be determined by parser
                        system="Unknown",
                        timestamp=dtc.timestamp
                    ) for dtc in obd_dtcs
                ])
            
            # Also try CAN if available
            if can_reader.is_connected:
                # Scan common ECUs for DTCs
                ecu_ids = [0x7E0, 0x7E1, 0x7E2, 0x7E3]  # Engine, Trans, ABS, Airbag
                for ecu_id in ecu_ids:
                    try:
                        can_dtcs = await can_reader.read_dtc_information(ecu_id)
                        for dtc_code in can_dtcs:
                            dtcs.append(DTCResponse(
                                code=dtc_code,
                                description=f"CAN DTC from ECU {ecu_id:03X}",
                                status="stored",
                                severity=DTCSeverity.UNKNOWN,
                                system="CAN",
                                timestamp=datetime.now()
                            ))
                    except:
                        continue  # Skip non-responsive ECUs
        
        # Read parameters
        if request.include_parameters and obd_reader.is_connected:
            if request.parameter_pids:
                obd_params = await obd_reader.read_multiple_parameters(request.parameter_pids)
            else:
                # Read common parameters
                common_pids = ["0x0C", "0x0D", "0x05", "0x0B", "0x04"]  # RPM, Speed, Coolant temp, MAP, Load
                obd_params = await obd_reader.read_multiple_parameters(common_pids)
            
            parameters.extend([
                ParameterResponse(
                    pid=param.pid,
                    name=param.name,
                    value=param.value,
                    unit=param.unit,
                    timestamp=param.timestamp
                ) for param in obd_params
            ])
        
        # Parse DTCs for additional information
        for dtc_response in dtcs:
            dtc_info = dtc_parser.parse_dtc(dtc_response.code)
            dtc_response.severity = DTCSeverity(dtc_info.severity.lower())
            dtc_response.system = dtc_info.system
            dtc_response.possible_causes = dtc_info.possible_causes
            dtc_response.repair_hints = dtc_info.repair_hints
            dtc_response.related_pids = dtc_info.related_pids
        
        scan_duration = (datetime.now() - start_time).total_seconds()
        
        return DiagnosticScanResponse(
            status=DiagnosticStatus.COMPLETE,
            dtcs=dtcs,
            parameters=parameters,
            scan_duration=scan_duration,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Diagnostic scan error: {e}")
        return DiagnosticScanResponse(
            status=DiagnosticStatus.ERROR,
            error_message=str(e),
            timestamp=datetime.now()
        )


@router.post("/dtcs/clear")
async def clear_dtcs(request: ClearDTCRequest):
    """
    Clear stored DTCs
    """
    try:
        if not request.confirm:
            raise HTTPException(status_code=400, detail="Confirmation required to clear DTCs")
        
        if not obd_reader.is_connected:
            raise HTTPException(status_code=400, detail="No OBD connection available")
        
        success = await obd_reader.clear_dtcs()
        
        if success:
            return {"message": "DTCs cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear DTCs")
            
    except Exception as e:
        logger.error(f"Clear DTCs error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# AI Analysis
@router.post("/analyze", response_model=AIAnalysisResponse)
async def analyze_diagnostics(
    request: AIAnalysisRequest,
    dtcs: List[str] = Query([], description="DTC codes to analyze"),
    background_tasks: BackgroundTasks = None
):
    """
    Perform AI-powered diagnostic analysis
    """
    try:
        if not dtcs:
            # Get current DTCs if none provided
            if obd_reader.is_connected:
                current_dtcs = await obd_reader.read_dtcs()
                dtcs = [dtc.code for dtc in current_dtcs]
        
        if not dtcs:
            raise HTTPException(status_code=400, detail="No DTCs to analyze")
        
        # Parse DTCs
        dtc_infos = [dtc_parser.parse_dtc(code) for code in dtcs]
        
        # Get current parameters if available
        parameters = []
        if obd_reader.is_connected:
            common_pids = ["0x0C", "0x0D", "0x05", "0x0B", "0x04"]
            parameters = await obd_reader.read_multiple_parameters(common_pids)
        
        # Build diagnostic context
        vehicle_context = VehicleContext()
        if request.vehicle_info:
            vehicle_context.make = request.vehicle_info.make
            vehicle_context.model = request.vehicle_info.model
            vehicle_context.year = request.vehicle_info.year
            vehicle_context.vin = request.vehicle_info.vin
            vehicle_context.engine_type = request.vehicle_info.engine_type
            vehicle_context.mileage = request.vehicle_info.mileage
            vehicle_context.fuel_type = request.vehicle_info.fuel_type
        
        diagnostic_context = DiagnosticContext(
            dtcs=dtc_infos,
            parameters=parameters,
            vehicle_info=vehicle_context
        )
        
        # Perform AI analysis
        if request.include_brand_specific and vehicle_context.make:
            brand_key = vehicle_context.make.lower()
            if brand_key in brand_profiles:
                brand_profile = brand_profiles[brand_key]
                brand_knowledge = brand_profile.get_brand_knowledge(
                    vehicle_context.model or "", 
                    vehicle_context.year
                )
                analysis = await dtc_explainer.explain_brand_specific(diagnostic_context, brand_knowledge)
            else:
                analysis = await dtc_explainer.explain_dtcs(diagnostic_context)
        else:
            analysis = await dtc_explainer.explain_dtcs(diagnostic_context)
        
        # Convert to response format
        repair_recommendations = [
            RepairRecommendation(
                description=rec["description"],
                priority=rec["priority"],
                estimated_time=rec.get("estimated_time"),
                parts_needed=rec.get("parts_needed", [])
            ) for rec in analysis.repair_recommendations
        ]
        
        # Get cost estimates if requested
        cost_estimates = None
        if request.include_cost_estimate:
            cost_data = await dtc_explainer.estimate_repair_costs(
                diagnostic_context, 
                [rec.description for rec in repair_recommendations]
            )
            cost_estimates = CostEstimate(
                total_low=cost_data.get("total_estimate_low", 0),
                total_high=cost_data.get("total_estimate_high", 0),
                parts_cost_low=0,  # Would need more detailed parsing
                parts_cost_high=0,
                labor_cost_low=0,
                labor_cost_high=0,
                breakdown=cost_data.get("breakdown", []),
                notes=cost_data.get("notes")
            )
        
        # Get maintenance plan if requested
        maintenance_plan = []
        if request.include_maintenance_plan:
            maintenance_data = await dtc_explainer.generate_maintenance_plan(diagnostic_context)
            for category, items in maintenance_data.items():
                if category != "notes":
                    for item in items:
                        maintenance_plan.append(MaintenanceItem(
                            description=item,
                            priority=category,
                            category=category
                        ))
        
        return AIAnalysisResponse(
            summary=analysis.summary,
            confidence_score=analysis.confidence_score,
            priority_issues=analysis.priority_issues,
            repair_recommendations=repair_recommendations,
            cost_estimates=cost_estimates,
            safety_warnings=analysis.safety_warnings,
            preventive_measures=analysis.preventive_measures,
            maintenance_plan=maintenance_plan,
            processing_time=analysis.ai_response.processing_time,
            model_used=analysis.ai_response.model_used
        )
        
    except Exception as e:
        logger.error(f"AI analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Brand Profiles
@router.get("/brands/{brand_name}", response_model=BrandProfileResponse)
async def get_brand_profile(brand_name: str, model: Optional[str] = None, year: Optional[int] = None):
    """
    Get brand-specific diagnostic information
    """
    try:
        brand_key = brand_name.lower()
        if brand_key not in brand_profiles:
            raise HTTPException(status_code=404, detail="Brand profile not found")
        
        profile = brand_profiles[brand_key]
        knowledge = profile.get_brand_knowledge(model or "", year)
        
        return BrandProfileResponse(
            brand_name=profile.brand_name,
            supported_models=getattr(profile, 'supported_models', []),
            common_issues=knowledge.get("common_issues", []),
            recalls=knowledge.get("recalls", []),
            service_bulletins=knowledge.get("service_bulletins", []),
            special_procedures=knowledge.get("special_procedures", []),
            diagnostic_tips=knowledge.get("diagnostic_tips", []),
            required_tools=knowledge.get("required_tools", [])
        )
        
    except Exception as e:
        logger.error(f"Brand profile error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/brands")
async def list_supported_brands():
    """
    List all supported vehicle brands
    """
    try:
        brands = []
        for brand_key, profile in brand_profiles.items():
            brands.append({
                "key": brand_key,
                "name": profile.brand_name,
                "supported_models": getattr(profile, 'supported_models', [])
            })
        
        return {"brands": brands}
        
    except Exception as e:
        logger.error(f"Brand listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# CAN Bus Operations
@router.post("/can/scan", response_model=CANScanResponse)
async def scan_can_bus():
    """
    Scan CAN bus for available ECUs
    """
    try:
        if not can_reader.is_connected:
            raise HTTPException(status_code=400, detail="CAN interface not connected")
        
        start_time = datetime.now()
        ecus = await can_reader.scan_ecus()
        scan_duration = (datetime.now() - start_time).total_seconds()
        
        ecu_responses = [
            ECUInfoResponse(
                ecu_id=f"{ecu.ecu_id:03X}",
                name=ecu.name,
                software_version=ecu.software_version,
                hardware_version=ecu.hardware_version,
                supplier_id=ecu.supplier_id,
                serial_number=ecu.serial_number
            ) for ecu in ecus
        ]
        
        return CANScanResponse(
            ecus_found=ecu_responses,
            scan_duration=scan_duration,
            timestamp=datetime.now(),
            total_ecus=len(ecus)
        )
        
    except Exception as e:
        logger.error(f"CAN scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# System Status
@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """
    Get overall system status
    """
    try:
        return SystemStatus(
            obd_connected=obd_reader.is_connected,
            can_connected=can_reader.is_connected,
            ai_available=dtc_explainer.openai_client is not None or dtc_explainer.local_llm is not None,
            database_connected=True,  # Would check actual database connection
            system_health="healthy"
        )
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Vehicle Detection
@router.get("/vehicle/detect")
async def detect_vehicle():
    """
    Detect vehicle make, model, and year from OBD connection or mock data
    """
    try:
        # Check if we have a real OBD connection
        if obd_reader.is_connected:
            vehicle_info = await obd_reader.detect_vehicle_info()

            if vehicle_info:
                return {
                    "make": vehicle_info.make,
                    "model": vehicle_info.model,
                    "year": vehicle_info.year,
                    "vin": vehicle_info.vin,
                    "engine_type": vehicle_info.engine_type,
                    "fuel_type": vehicle_info.fuel_type,
                    "confidence": vehicle_info.confidence,
                    "detection_method": vehicle_info.detection_method
                }
            else:
                return {
                    "make": None,
                    "model": None,
                    "year": None,
                    "vin": None,
                    "confidence": 0.0,
                    "detection_method": "failed"
                }
        else:
            # Mock mode - return mock vehicle information
            from .frontend_routes import MOCK_SCENARIOS
            mock_scenario = MOCK_SCENARIOS[0]  # Use first mock scenario
            vehicle_info = mock_scenario.vehicle_info

            return {
                "make": vehicle_info.make,
                "model": vehicle_info.model,
                "year": vehicle_info.year,
                "vin": vehicle_info.vin or f"MOCK{vehicle_info.year}{vehicle_info.make[:3].upper()}123456",
                "engine_type": vehicle_info.engine,
                "fuel_type": vehicle_info.fuel_type,
                "confidence": 0.95,
                "detection_method": "mock_data"
            }

    except Exception as e:
        logger.error(f"Vehicle detection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/vehicle/info")
async def get_current_vehicle():
    """
    Get currently detected vehicle information
    """
    try:
        vehicle_info = obd_reader.get_detected_vehicle()

        if vehicle_info:
            return {
                "make": vehicle_info.make,
                "model": vehicle_info.model,
                "year": vehicle_info.year,
                "vin": vehicle_info.vin,
                "engine_type": vehicle_info.engine_type,
                "fuel_type": vehicle_info.fuel_type,
                "confidence": vehicle_info.confidence,
                "detection_method": vehicle_info.detection_method
            }
        else:
            return {
                "make": None,
                "model": None,
                "year": None,
                "vin": None,
                "confidence": 0.0,
                "detection_method": "not_detected"
            }

    except Exception as e:
        logger.error(f"Get vehicle info error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# New Enhanced Features

@router.post("/knowledge/import")
async def import_reference_knowledge(background_tasks: BackgroundTasks):
    """
    Import diagnostic knowledge from references folder
    """
    try:
        # Run migration in background
        background_tasks.add_task(database_migrator.migrate_all_references)

        return {
            "message": "Knowledge import started in background",
            "status": "processing"
        }

    except Exception as e:
        logger.error(f"Knowledge import error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/knowledge/import/status")
async def get_import_status():
    """
    Get knowledge import status
    """
    try:
        stats = database_migrator.stats
        return {
            "status": "completed" if stats.get('errors', 0) == 0 else "completed_with_errors",
            "statistics": stats
        }

    except Exception as e:
        logger.error(f"Import status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai/gemini/initialize")
async def initialize_gemini():
    """
    Initialize Google Gemini AI integration
    """
    try:
        success = await gemini_integration.initialize()

        if success:
            model_info = await gemini_integration.get_model_info()
            return {
                "initialized": True,
                "model_info": model_info
            }
        else:
            return {
                "initialized": False,
                "error": "Failed to initialize Gemini AI"
            }

    except Exception as e:
        logger.error(f"Gemini initialization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai/gemini/analyze")
async def analyze_with_gemini(
    dtc_codes: List[str],
    vehicle_info: Optional[Dict[str, Any]] = None,
    parameters: Optional[List[Dict[str, Any]]] = None
):
    """
    Analyze DTCs using Google Gemini AI
    """
    try:
        if not gemini_integration.is_initialized:
            raise HTTPException(status_code=400, detail="Gemini AI not initialized")

        vehicle_data = vehicle_info or {}
        param_data = parameters or []

        response = await gemini_integration.analyze_dtc_codes(
            dtc_codes, vehicle_data, param_data
        )

        return {
            "analysis": response.content,
            "model_used": response.model_used,
            "processing_time": response.processing_time,
            "confidence_score": response.confidence_score
        }

    except Exception as e:
        logger.error(f"Gemini analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection/enhanced/status")
async def get_enhanced_connection_status():
    """
    Get enhanced connection status with retry/failover info
    """
    try:
        conn_info = connection_manager.get_connection_info()
        stats = connection_manager.get_statistics()
        health = await connection_manager.health_check()

        return {
            "connection_info": conn_info,
            "statistics": stats,
            "health": health
        }

    except Exception as e:
        logger.error(f"Enhanced connection status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/connection/enhanced/connect")
async def enhanced_connect(port: Optional[str] = None, baudrate: int = 38400):
    """
    Connect using enhanced connection manager with retry/failover
    """
    try:
        success = await connection_manager.connect(port, baudrate)

        if success:
            conn_info = connection_manager.get_connection_info()
            return {
                "connected": True,
                "connection_info": conn_info
            }
        else:
            return {
                "connected": False,
                "error": "Failed to establish connection"
            }

    except Exception as e:
        logger.error(f"Enhanced connection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/safety/status")
async def get_safety_status():
    """
    Get OBD safety system status
    """
    try:
        expert_status = safety_manager.get_expert_mode_status()
        safety_summary = safety_manager.get_safety_summary()

        return {
            "expert_mode": expert_status,
            "safety_summary": safety_summary
        }

    except Exception as e:
        logger.error(f"Safety status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/safety/expert/enable")
async def enable_expert_mode(confirmation_code: str):
    """
    Enable expert mode for dangerous operations
    """
    try:
        success = safety_manager.enable_expert_mode(confirmation_code)

        if success:
            status = safety_manager.get_expert_mode_status()
            return {
                "enabled": True,
                "status": status
            }
        else:
            return {
                "enabled": False,
                "error": "Invalid confirmation code"
            }

    except Exception as e:
        logger.error(f"Expert mode enable error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/safety/expert/disable")
async def disable_expert_mode():
    """
    Disable expert mode
    """
    try:
        safety_manager.disable_expert_mode()
        status = safety_manager.get_expert_mode_status()

        return {
            "disabled": True,
            "status": status
        }

    except Exception as e:
        logger.error(f"Expert mode disable error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/safety/check")
async def check_command_safety(command: str, context: Optional[Dict[str, Any]] = None):
    """
    Check if OBD command is safe to execute
    """
    try:
        safety_check = safety_manager.check_command_safety(command, context)

        return safety_check

    except Exception as e:
        logger.error(f"Safety check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dtc/lookup")
async def lookup_dtc_with_ai_fallback(
    dtc_codes: List[str],
    vehicle_info: Optional[Dict[str, Any]] = None,
    use_ai_fallback: bool = True
):
    """
    Lookup DTC codes with AI fallback for unknown codes
    """
    try:
        from ..database.dtc_repository import dtc_repository

        if use_ai_fallback:
            # Use AI fallback for unknown codes
            dtcs = await dtc_repository.get_multiple_dtcs_with_ai_fallback(dtc_codes, vehicle_info)
        else:
            # Only use database lookup
            dtcs = []
            for code in dtc_codes:
                dtc = dtc_repository.get_dtc_by_code(code)
                if dtc:
                    dtcs.append(dtc)

        # DTCs are now returned as dicts, so we can use them directly
        results = []
        for dtc in dtcs:
            if isinstance(dtc, dict):
                # Already a dict from the repository
                results.append(dtc)
            else:
                # Convert object to dict (fallback)
                try:
                    result = {
                        "code": str(dtc.code) if hasattr(dtc, 'code') else "Unknown",
                        "description": str(dtc.description) if hasattr(dtc, 'description') else "No description",
                        "category": str(dtc.category) if hasattr(dtc, 'category') else "Unknown",
                        "severity": str(dtc.severity) if hasattr(dtc, 'severity') else "medium",
                        "system": str(dtc.system) if hasattr(dtc, 'system') else "Unknown",
                        "possible_causes": list(dtc.possible_causes) if hasattr(dtc, 'possible_causes') and dtc.possible_causes else [],
                        "repair_hints": list(dtc.repair_hints) if hasattr(dtc, 'repair_hints') and dtc.repair_hints else [],
                        "related_pids": list(dtc.related_pids) if hasattr(dtc, 'related_pids') and dtc.related_pids else [],
                        "freeze_frame_required": bool(dtc.freeze_frame_required) if hasattr(dtc, 'freeze_frame_required') else False,
                        "obd2_standard": bool(dtc.obd2_standard) if hasattr(dtc, 'obd2_standard') else False,
                        "source": "database" if (hasattr(dtc, 'id') and dtc.id and dtc.id > 0) else "ai_fallback",
                        "brand_specific_info": dict(dtc.brand_specific_info) if hasattr(dtc, 'brand_specific_info') and dtc.brand_specific_info else {}
                    }
                    results.append(result)
                except Exception as dtc_error:
                    logger.error(f"Error processing DTC: {dtc_error}")
                    # Add minimal fallback result
                    results.append({
                        "code": "Unknown",
                        "description": "Error processing DTC",
                        "category": "Unknown",
                        "severity": "medium",
                        "system": "Unknown",
                        "possible_causes": [],
                        "repair_hints": [],
                        "related_pids": [],
                        "freeze_frame_required": False,
                        "obd2_standard": False,
                        "source": "error",
                        "brand_specific_info": {}
                    })

        return {
            "dtc_codes": results,
            "total_found": len(results),
            "ai_fallback_used": use_ai_fallback,
            "unknown_codes": [code for code in dtc_codes if not any(r["code"] == code for r in results)]
        }

    except Exception as e:
        logger.error(f"DTC lookup error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dtc/ai-fallback/stats")
async def get_ai_fallback_stats():
    """
    Get AI fallback system statistics
    """
    try:
        from ..dtc_analysis.ai_fallback import dtc_ai_fallback

        stats = await dtc_ai_fallback.get_statistics()

        return {
            "ai_fallback_stats": stats,
            "status": "active" if gemini_integration.is_initialized else "inactive"
        }

    except Exception as e:
        logger.error(f"AI fallback stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dtc/ai-fallback/promote")
async def promote_ai_dtc_to_database(dtc_code: str, verify: bool = False):
    """
    Promote AI-generated DTC to main database
    """
    try:
        from ..dtc_analysis.ai_fallback import dtc_ai_fallback

        success = await dtc_ai_fallback.promote_to_main_database(dtc_code, verify)

        if success:
            return {
                "promoted": True,
                "dtc_code": dtc_code,
                "verified": verify
            }
        else:
            return {
                "promoted": False,
                "dtc_code": dtc_code,
                "error": "Failed to promote or insufficient confidence"
            }

    except Exception as e:
        logger.error(f"DTC promotion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dtc/ai-fallback/cleanup")
async def cleanup_ai_cache(days_old: int = 30):
    """
    Clean up old AI cache entries
    """
    try:
        from ..dtc_analysis.ai_fallback import dtc_ai_fallback

        deleted_count = await dtc_ai_fallback.cleanup_old_cache(days_old)

        return {
            "cleaned_up": True,
            "deleted_entries": deleted_count,
            "days_old": days_old
        }

    except Exception as e:
        logger.error(f"AI cache cleanup error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Include frontend routes
router.include_router(frontend_router)
