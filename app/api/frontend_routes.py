"""
Frontend-specific API routes for Next.js integration
Provides simplified endpoints optimized for web frontend
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel, Field

from .models import *
from ..obd_interface.obd_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, DTCResult, OBDParameter
from ..ai_engine.explain_dtc import DTCExplainer
from ..ai_engine.prompt_builder import Diagnostic<PERSON>ontext, VehicleContext
from ..database.database import db_manager
from ..database.models import DiagnosticSession as DBDiagnosticSession
from ..config import settings

logger = logging.getLogger(__name__)

# Create router for frontend-specific endpoints
frontend_router = APIRouter(prefix="/api", tags=["frontend"])

# Global state for current OBD connection
current_obd_reader: Optional[OBDReader] = None
active_sessions: Dict[str, Dict[str, Any]] = {}

# Pydantic models for frontend API
class ConnectionRequest(BaseModel):
    connection_type: str = Field(..., description="Type of connection: usb, bluetooth, mock")
    port: Optional[str] = Field(None, description="Port for USB connection")
    mock_scenario: Optional[str] = Field(None, description="Mock scenario ID")
    vehicle_info: Optional[VehicleInfo] = Field(None, description="Optional vehicle information")

class ScanOptionsRequest(BaseModel):
    include_freeze_frame: bool = Field(True, description="Include freeze frame data")
    include_pending_codes: bool = Field(True, description="Include pending DTCs")
    include_permanent_codes: bool = Field(True, description="Include permanent DTCs")
    read_live_data: bool = Field(True, description="Read live parameter data")
    ai_analysis: bool = Field(True, description="Perform AI analysis")

class ConnectionStatusResponse(BaseModel):
    connected: bool
    port: Optional[str] = None
    protocol: Optional[str] = None
    vehicle_info: Optional[VehicleInfo] = None
    error_message: Optional[str] = None
    last_activity: Optional[str] = None

class MockScenario(BaseModel):
    id: str
    name: str
    description: str
    vehicle_info: VehicleInfo
    dtc_codes: List[DTCCode]
    parameters: List[OBDParameter]

# Mock scenarios for testing
MOCK_SCENARIOS = [
    MockScenario(
        id="toyota_engine_misfire",
        name="Toyota Engine Misfire",
        description="Toyota Camry 2018 with engine misfire issues",
        vehicle_info=VehicleInfo(
            make="Toyota",
            model="Camry",
            year=2018,
            engine="2.5L 4-Cylinder",
            fuel_type="Gasoline"
        ),
        dtc_codes=[
            DTCCode(
                code="P0301",
                description="Cylinder 1 Misfire Detected",
                severity="high",
                category="Engine",
                possible_causes=[
                    "Faulty spark plug",
                    "Ignition coil failure",
                    "Fuel injector problem",
                    "Low compression"
                ],
                repair_hints=[
                    "Check spark plug condition",
                    "Test ignition coil resistance",
                    "Inspect fuel injector operation"
                ]
            ),
            DTCCode(
                code="P0171",
                description="System Too Lean (Bank 1)",
                severity="medium",
                category="Fuel System",
                possible_causes=[
                    "Vacuum leak",
                    "Mass airflow sensor malfunction",
                    "Fuel pump weak pressure",
                    "Dirty fuel injectors"
                ],
                repair_hints=[
                    "Check for vacuum leaks",
                    "Clean or replace MAF sensor",
                    "Test fuel pressure"
                ]
            )
        ],
        parameters=[
            OBDParameter(
                pid="010C",
                name="Engine RPM",
                value=750,
                unit="rpm",
                timestamp=datetime.now()
            ),
            OBDParameter(
                pid="010D",
                name="Vehicle Speed",
                value=0,
                unit="km/h",
                timestamp=datetime.now()
            )
        ]
    ),
    MockScenario(
        id="bmw_emission_system",
        name="BMW Emission System",
        description="BMW 3 Series 2020 with emission system warnings",
        vehicle_info=VehicleInfo(
            make="BMW",
            model="3 Series",
            year=2020,
            engine="2.0L Turbo",
            fuel_type="Gasoline"
        ),
        dtc_codes=[
            DTCCode(
                code="P0420",
                description="Catalyst System Efficiency Below Threshold",
                severity="medium",
                category="Emission",
                possible_causes=[
                    "Catalytic converter degradation",
                    "Oxygen sensor malfunction",
                    "Engine running rich/lean"
                ],
                repair_hints=[
                    "Test oxygen sensor response",
                    "Check catalytic converter efficiency",
                    "Verify fuel trim values"
                ]
            )
        ],
        parameters=[
            OBDParameter(
                pid="010C",
                name="Engine RPM",
                value=800,
                unit="rpm",
                timestamp=datetime.now()
            )
        ]
    ),
    MockScenario(
        id="vag_transmission_fault",
        name="VAG Transmission Fault",
        description="Volkswagen Golf 2019 with transmission control issues",
        vehicle_info=VehicleInfo(
            make="Volkswagen",
            model="Golf",
            year=2019,
            engine="1.4L TSI",
            fuel_type="Gasoline"
        ),
        dtc_codes=[
            DTCCode(
                code="P0700",
                description="Transmission Control System Malfunction",
                severity="high",
                category="Transmission",
                possible_causes=[
                    "TCM internal fault",
                    "Wiring harness damage",
                    "Solenoid valve failure",
                    "Hydraulic pressure issues"
                ],
                repair_hints=[
                    "Scan transmission control module",
                    "Check transmission fluid level",
                    "Inspect wiring connections"
                ]
            ),
            DTCCode(
                code="P0016",
                description="Crankshaft Position - Camshaft Position Correlation",
                severity="critical",
                category="Engine Timing",
                possible_causes=[
                    "Timing chain stretched",
                    "Timing chain tensioner failure",
                    "Camshaft position sensor fault",
                    "Crankshaft position sensor fault"
                ],
                repair_hints=[
                    "Check timing chain tension",
                    "Inspect timing chain guides",
                    "Test position sensors"
                ]
            )
        ],
        parameters=[
            OBDParameter(
                pid="010C",
                name="Engine RPM",
                value=850,
                unit="rpm",
                timestamp=datetime.now()
            )
        ]
    )
]

@frontend_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@frontend_router.get("/obd/ports")
async def get_available_ports():
    """Get list of available serial ports"""
    try:
        import serial.tools.list_ports
        ports = [port.device for port in serial.tools.list_ports.comports()]
        return {
            "success": True,
            "data": ports
        }
    except Exception as e:
        logger.error(f"Error getting ports: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": []
        }

@frontend_router.post("/obd/connect")
async def connect_to_vehicle(request: ConnectionRequest):
    """Connect to vehicle via OBD2"""
    global current_obd_reader
    
    try:
        if request.connection_type == "mock":
            # Handle mock connection
            return {
                "success": True,
                "data": ConnectionStatusResponse(
                    connected=True,
                    port="MOCK",
                    protocol="Mock Protocol",
                    vehicle_info=None,
                    last_activity=datetime.now().isoformat()
                )
            }
        
        # Real OBD connection
        current_obd_reader = OBDReader(port=request.port)
        connected = await current_obd_reader.connect()
        
        if connected:
            # Get vehicle info if available
            vehicle_info = None
            if current_obd_reader.connection:
                try:
                    # Try to get basic vehicle info
                    vin_response = current_obd_reader.connection.query(obd.commands.VIN)
                    if vin_response.value:
                        vehicle_info = VehicleInfo(vin=str(vin_response.value))
                except:
                    pass
            
            return {
                "success": True,
                "data": ConnectionStatusResponse(
                    connected=True,
                    port=current_obd_reader.port,
                    protocol=current_obd_reader.protocol_manager.current_protocol.name if current_obd_reader.protocol_manager.current_protocol else None,
                    vehicle_info=vehicle_info,
                    last_activity=datetime.now().isoformat()
                )
            }
        else:
            raise Exception("Failed to establish OBD connection")
            
    except Exception as e:
        logger.error(f"Connection error: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@frontend_router.post("/obd/disconnect")
async def disconnect_from_vehicle():
    """Disconnect from vehicle"""
    global current_obd_reader
    
    try:
        if current_obd_reader:
            await current_obd_reader.disconnect()
            current_obd_reader = None
        
        return {
            "success": True,
            "message": "Disconnected successfully"
        }
    except Exception as e:
        logger.error(f"Disconnect error: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@frontend_router.get("/obd/status")
async def get_connection_status():
    """Get current connection status"""
    global current_obd_reader
    
    if current_obd_reader and current_obd_reader.is_connected:
        return {
            "success": True,
            "data": ConnectionStatusResponse(
                connected=True,
                port=current_obd_reader.port,
                protocol=current_obd_reader.protocol_manager.current_protocol.name if current_obd_reader.protocol_manager.current_protocol else None,
                last_activity=datetime.now().isoformat()
            )
        }
    else:
        return {
            "success": True,
            "data": ConnectionStatusResponse(
                connected=False
            )
        }

@frontend_router.get("/mock/scenarios")
async def get_mock_scenarios():
    """Get available mock scenarios for testing"""
    return {
        "success": True,
        "data": MOCK_SCENARIOS
    }

@frontend_router.post("/diagnostic/scan")
async def start_diagnostic_scan(request: ScanOptionsRequest, background_tasks: BackgroundTasks):
    """Start a new diagnostic scan"""
    global current_obd_reader, active_sessions
    
    try:
        # Generate session ID
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(active_sessions)}"
        
        # Initialize session
        session_data = {
            "id": session_id,
            "status": "connecting",
            "created_at": datetime.now().isoformat(),
            "vehicle_info": {},
            "dtc_codes": [],
            "parameters": [],
            "ai_analysis": None,
            "error_message": None
        }
        
        active_sessions[session_id] = session_data
        
        # Start background scan task
        background_tasks.add_task(perform_diagnostic_scan, session_id, request)
        
        return {
            "success": True,
            "data": {"session_id": session_id}
        }
        
    except Exception as e:
        logger.error(f"Error starting scan: {e}")
        return {
            "success": False,
            "error": str(e)
        }

async def perform_diagnostic_scan(session_id: str, options: ScanOptionsRequest):
    """Background task to perform diagnostic scan"""
    global current_obd_reader, active_sessions
    
    try:
        session = active_sessions[session_id]
        
        # Simulate scan stages
        session["status"] = "scanning"
        await asyncio.sleep(2)  # Simulate scanning time
        
        # For mock data, use predefined scenarios
        if not current_obd_reader:
            # Use first mock scenario
            mock_scenario = MOCK_SCENARIOS[0]
            session["vehicle_info"] = mock_scenario.vehicle_info.dict()
            session["dtc_codes"] = [code.dict() for code in mock_scenario.dtc_codes]
            session["parameters"] = [param.dict() for param in mock_scenario.parameters]
        else:
            # Real OBD scan
            dtc_results = await current_obd_reader.read_dtc_codes()
            session["dtc_codes"] = [result.dict() for result in dtc_results]
            
            # Read some basic parameters
            parameters = []
            basic_pids = ["010C", "010D", "0105"]  # RPM, Speed, Coolant temp
            for pid in basic_pids:
                param = await current_obd_reader.read_parameter(pid)
                if param:
                    parameters.append(param.dict())
            session["parameters"] = parameters
        
        # AI Analysis stage
        if options.ai_analysis:
            session["status"] = "analyzing"
            await asyncio.sleep(2)  # Simulate AI processing

            # Get AI analysis for DTCs using Gemini
            try:
                from ..ai_engine.gemini_integration import gemini_integration

                # Initialize Gemini if not already done
                if not gemini_integration.is_initialized:
                    await gemini_integration.initialize()

                if gemini_integration.is_initialized and session["dtc_codes"]:
                    # Extract DTC codes for AI analysis
                    dtc_codes = [dtc["code"] for dtc in session["dtc_codes"]]
                    vehicle_info = session.get("vehicle_info", {})

                    # Get AI analysis
                    ai_response = await gemini_integration.analyze_dtc_codes(
                        dtc_codes, vehicle_info, session.get("parameters", [])
                    )

                    session["ai_analysis"] = {
                        "summary": ai_response.content[:500] + "..." if len(ai_response.content) > 500 else ai_response.content,
                        "full_analysis": ai_response.content,
                        "confidence_score": ai_response.confidence_score,
                        "model_used": ai_response.model_used,
                        "processing_time": ai_response.processing_time,
                        "recommendations": [
                            {
                                "priority": "high",
                                "category": "immediate",
                                "title": "AI-Generated Recommendations",
                                "description": "Based on AI analysis of detected fault codes",
                                "estimated_cost": 200,
                                "estimated_time": "2-4 hours",
                                "difficulty": "moderate"
                            }
                        ]
                    }
                else:
                    # Fallback mock analysis
                    session["ai_analysis"] = {
                        "summary": "Vehicle shows signs of engine performance issues that require attention.",
                        "recommendations": [
                            {
                                "priority": "high",
                                "category": "immediate",
                                "title": "Check Engine Components",
                                "description": "Inspect spark plugs and ignition coils for proper operation.",
                                "estimated_cost": 150,
                                "estimated_time": "1-2 hours",
                                "difficulty": "moderate"
                            }
                        ],
                        "confidence_score": 0.85,
                        "model_used": "mock_analysis",
                        "processing_time": 2.0
                    }
            except Exception as e:
                logger.error(f"AI analysis error: {e}")
                # Fallback to mock analysis
                session["ai_analysis"] = {
                    "summary": "AI analysis temporarily unavailable. Manual diagnostic recommended.",
                    "error": str(e),
                    "confidence_score": 0.5,
                    "model_used": "fallback",
                    "processing_time": 1.0
                }
        
        session["status"] = "completed"
        session["completed_at"] = datetime.now().isoformat()
        
    except Exception as e:
        logger.error(f"Error in diagnostic scan: {e}")
        session["status"] = "error"
        session["error_message"] = str(e)

@frontend_router.get("/diagnostic/session/{session_id}")
async def get_diagnostic_session(session_id: str):
    """Get diagnostic session data"""
    global active_sessions
    
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "success": True,
        "data": active_sessions[session_id]
    }

@frontend_router.post("/diagnostic/session/{session_id}/stop")
async def stop_diagnostic_scan(session_id: str):
    """Stop an active diagnostic scan"""
    global active_sessions
    
    if session_id in active_sessions:
        active_sessions[session_id]["status"] = "stopped"
        return {
            "success": True,
            "message": "Scan stopped"
        }
    else:
        raise HTTPException(status_code=404, detail="Session not found")

@frontend_router.get("/diagnostic/history")
async def get_diagnostic_history(limit: int = 10):
    """Get diagnostic session history"""
    global active_sessions
    
    # Return recent sessions (in a real app, this would come from database)
    sessions = list(active_sessions.values())
    sessions.sort(key=lambda x: x["created_at"], reverse=True)
    
    return {
        "success": True,
        "data": {
            "sessions": sessions[:limit],
            "total_sessions": len(sessions),
            "last_scan": sessions[0]["created_at"] if sessions else None
        }
    }


@frontend_router.post("/dtc/analyze")
async def analyze_dtc_with_ai(request: dict):
    """Analyze specific DTC codes with AI"""
    try:
        dtc_codes = request.get("dtc_codes", [])
        vehicle_info = request.get("vehicle_info", {})

        if not dtc_codes:
            raise HTTPException(status_code=400, detail="No DTC codes provided")

        from ..ai_engine.gemini_integration import gemini_integration

        # Initialize Gemini if not already done
        if not gemini_integration.is_initialized:
            await gemini_integration.initialize()

        if not gemini_integration.is_initialized:
            return {
                "success": False,
                "error": "AI analysis service not available"
            }

        # Get AI analysis
        ai_response = await gemini_integration.analyze_dtc_codes(
            dtc_codes, vehicle_info, []
        )

        return {
            "success": True,
            "data": {
                "analysis": ai_response.content,
                "confidence_score": ai_response.confidence_score,
                "model_used": ai_response.model_used,
                "processing_time": ai_response.processing_time,
                "dtc_codes": dtc_codes
            }
        }

    except Exception as e:
        logger.error(f"DTC AI analysis error: {e}")
        return {
            "success": False,
            "error": str(e)
        }
