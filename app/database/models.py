"""
SQLAlchemy database models for OBD2 diagnostic system
"""
try:
    from sqlalchemy import Column, Integer, String, Text, Float, Boolean, DateTime, JSON, ForeignKey, Index
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import relationship
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    print("Warning: SQLAlchemy not available for models. Install with: pip install sqlalchemy")
    SQLALCHEMY_AVAILABLE = False
    # Mock classes for development
    def Column(*args, **kwargs):
        return None

    Integer = String = Text = Float = Boolean = DateTime = JSON = ForeignKey = Index = None

    def declarative_base():
        return type('MockBase', (), {})

    def relationship(*args, **kwargs):
        return None
try:
    from sqlalchemy.sql import func
except ImportError:
    func = type('MockFunc', (), {
        'now': lambda: None
    })()
from datetime import datetime

Base = declarative_base()


class DTCCode(Base):
    """DTC (Diagnostic Trouble Code) definitions table"""
    __tablename__ = "dtc_codes"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(10), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=False)
    category = Column(String(50), nullable=False, index=True)  # Powertrain, Body, Chassis, Network
    severity = Column(String(20), nullable=False, index=True)  # critical, high, medium, low, info
    system = Column(String(100), nullable=False, index=True)   # Engine, Transmission, ABS, etc.

    # AI Enhancement fields
    source = Column(String(50), nullable=True, index=True)  # database, google_gemini, openai, etc.
    trust_score = Column(Float, nullable=True)  # 0.0 to 1.0 confidence score
    
    # JSON fields for flexible data storage
    possible_causes = Column(JSON)  # List of possible causes
    repair_hints = Column(JSON)     # List of repair hints
    related_pids = Column(JSON)     # List of related PID codes
    
    # Additional metadata
    freeze_frame_required = Column(Boolean, default=False)
    obd2_standard = Column(Boolean, default=True)  # True for standard OBD2, False for manufacturer specific
    
    # Brand-specific information
    brand_specific_info = Column(JSON)  # Brand-specific notes and procedures
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    diagnostic_sessions = relationship("DiagnosticSession", secondary="session_dtc_association", back_populates="dtc_codes")
    
    def __repr__(self):
        return f"<DTCCode(code='{self.code}', description='{self.description[:50]}...')>"


class VehicleProfile(Base):
    """Vehicle profile information"""
    __tablename__ = "vehicle_profiles"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    make = Column(String(50), nullable=False, index=True)
    model = Column(String(50), nullable=False, index=True)
    year = Column(Integer, index=True)
    engine_type = Column(String(100))
    fuel_type = Column(String(20))  # gasoline, diesel, hybrid, electric
    transmission_type = Column(String(20))  # manual, automatic, cvt, dsg
    
    # VIN information
    vin = Column(String(17), unique=True, index=True)
    vin_decoded = Column(JSON)  # Decoded VIN information
    
    # Vehicle specifications
    engine_displacement = Column(Float)  # in liters
    power_hp = Column(Integer)
    power_kw = Column(Integer)
    torque_nm = Column(Integer)
    
    # Maintenance information
    current_mileage = Column(Integer)
    last_service_mileage = Column(Integer)
    last_service_date = Column(DateTime)
    
    # Additional metadata
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    diagnostic_sessions = relationship("DiagnosticSession", back_populates="vehicle")
    
    def __repr__(self):
        return f"<VehicleProfile(make='{self.make}', model='{self.model}', year={self.year})>"


class DiagnosticSession(Base):
    """Diagnostic session records"""
    __tablename__ = "diagnostic_sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), unique=True, nullable=False, index=True)
    
    # Vehicle information
    vehicle_id = Column(Integer, ForeignKey("vehicle_profiles.id"), index=True)
    
    # Session metadata
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime)
    duration_seconds = Column(Float)
    
    # Connection information
    connection_type = Column(String(20))  # usb, bluetooth, can, wifi
    adapter_info = Column(JSON)  # Adapter details
    protocol_used = Column(String(50))  # OBD2, UDS, KWP2000, etc.
    
    # Diagnostic results
    dtc_count = Column(Integer, default=0)
    dtc_codes_found = Column(JSON)  # List of DTC codes found
    parameters_read = Column(JSON)  # Parameters and their values
    freeze_frame_data = Column(JSON)  # Freeze frame data if available
    
    # AI Analysis results
    ai_analysis_performed = Column(Boolean, default=False)
    ai_model_used = Column(String(50))
    ai_confidence_score = Column(Float)
    ai_summary = Column(Text)
    ai_recommendations = Column(JSON)
    ai_cost_estimates = Column(JSON)
    ai_repair_suggestions = Column(JSON)  # Detailed repair recommendations
    ai_maintenance_plan = Column(JSON)    # Preventive maintenance suggestions
    
    # Status and notes
    status = Column(String(20), default="completed")  # connecting, scanning, analyzing, completed, error
    error_message = Column(Text)
    technician_notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    vehicle = relationship("VehicleProfile", back_populates="diagnostic_sessions")
    dtc_codes = relationship("DTCCode", secondary="session_dtc_association")
    
    def __repr__(self):
        return f"<DiagnosticSession(session_id='{self.session_id}', dtc_count={self.dtc_count})>"


class SessionDTCAssociation(Base):
    """Association table between diagnostic sessions and DTC codes"""
    __tablename__ = "session_dtc_association"
    
    session_id = Column(Integer, ForeignKey("diagnostic_sessions.id"), primary_key=True)
    dtc_id = Column(Integer, ForeignKey("dtc_codes.id"), primary_key=True)
    
    # Additional information about this specific DTC occurrence
    status = Column(String(20))  # stored, pending, intermittent
    occurrence_count = Column(Integer, default=1)
    first_detected = Column(DateTime, default=func.now())
    last_detected = Column(DateTime, default=func.now())
    
    # Freeze frame data specific to this DTC
    freeze_frame_data = Column(JSON)
    
    def __repr__(self):
        return f"<SessionDTCAssociation(session_id={self.session_id}, dtc_id={self.dtc_id})>"


class ExpertModeSession(Base):
    """Expert mode sessions for advanced operations"""
    __tablename__ = "expert_mode_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_token = Column(String(64), unique=True, nullable=False, index=True)
    user_identifier = Column(String(100), nullable=False)  # IP, device ID, etc.

    # Session details
    created_at = Column(DateTime, default=func.now())
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)

    # Permissions
    can_write_ecu = Column(Boolean, default=False)
    can_clear_dtc = Column(Boolean, default=True)
    can_reset_adaptations = Column(Boolean, default=False)
    can_perform_tests = Column(Boolean, default=True)

    # Audit trail
    operations_performed = Column(JSON, default=list)
    last_activity = Column(DateTime, default=func.now())

    def __repr__(self):
        return f"<ExpertModeSession(token={self.session_token[:8]}..., active={self.is_active})>"


class ECUParameter(Base):
    """ECU parameter definitions"""
    __tablename__ = "ecu_parameters"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    pid = Column(String(10), nullable=False, index=True)  # Parameter ID (e.g., "0x0C")
    name = Column(String(100), nullable=False)
    description = Column(Text)
    unit = Column(String(20))  # rpm, °C, %, V, etc.
    
    # Value processing
    formula = Column(String(200))  # Formula to convert raw value
    min_value = Column(Float)
    max_value = Column(Float)
    normal_range_min = Column(Float)
    normal_range_max = Column(Float)
    
    # Parameter metadata
    mode = Column(Integer, default=1)  # OBD2 mode (1, 2, etc.)
    response_length = Column(Integer)  # Expected response length in bytes
    update_frequency = Column(String(20))  # fast, medium, slow
    
    # Brand-specific information
    brand_specific = Column(Boolean, default=False)
    supported_brands = Column(JSON)  # List of brands that support this parameter
    brand_specific_info = Column(JSON)  # Brand-specific notes
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<ECUParameter(pid='{self.pid}', name='{self.name}')>"


class MaintenanceRecord(Base):
    """Vehicle maintenance records"""
    __tablename__ = "maintenance_records"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    vehicle_id = Column(Integer, ForeignKey("vehicle_profiles.id"), nullable=False, index=True)
    
    # Maintenance details
    service_type = Column(String(50), nullable=False)  # oil_change, brake_service, etc.
    description = Column(Text)
    mileage = Column(Integer)
    service_date = Column(DateTime, nullable=False)
    
    # Cost information
    parts_cost = Column(Float)
    labor_cost = Column(Float)
    total_cost = Column(Float)
    
    # Service provider
    shop_name = Column(String(100))
    technician_name = Column(String(100))
    
    # Related diagnostic session
    diagnostic_session_id = Column(Integer, ForeignKey("diagnostic_sessions.id"))
    
    # Additional information
    warranty_until = Column(DateTime)
    next_service_mileage = Column(Integer)
    next_service_date = Column(DateTime)
    notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    vehicle = relationship("VehicleProfile")
    diagnostic_session = relationship("DiagnosticSession")
    
    def __repr__(self):
        return f"<MaintenanceRecord(service_type='{self.service_type}', date='{self.service_date}')>"


class BrandProfile(Base):
    """Brand-specific diagnostic profiles"""
    __tablename__ = "brand_profiles"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    brand_name = Column(String(50), nullable=False, unique=True, index=True)
    
    # Brand information
    full_name = Column(String(100))
    country_of_origin = Column(String(50))
    parent_company = Column(String(50))
    
    # Supported models and years
    supported_models = Column(JSON)  # List of supported models
    year_range_start = Column(Integer)
    year_range_end = Column(Integer)
    
    # Diagnostic capabilities
    supported_protocols = Column(JSON)  # List of supported protocols
    special_procedures = Column(JSON)  # Brand-specific procedures
    common_issues = Column(JSON)  # Known common issues
    recalls_tsbs = Column(JSON)  # Recalls and technical service bulletins
    
    # Tools and requirements
    required_tools = Column(JSON)  # Special tools needed
    diagnostic_software = Column(JSON)  # Recommended diagnostic software
    
    # Additional metadata
    notes = Column(Text)
    active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<BrandProfile(brand_name='{self.brand_name}')>"


class SystemConfiguration(Base):
    """System configuration and settings"""
    __tablename__ = "system_configuration"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(100), nullable=False, unique=True, index=True)
    value = Column(Text)
    data_type = Column(String(20), default="string")  # string, integer, float, boolean, json
    category = Column(String(50), index=True)  # obd, ai, database, etc.
    
    # Metadata
    description = Column(Text)
    default_value = Column(Text)
    is_sensitive = Column(Boolean, default=False)  # For passwords, API keys, etc.
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<SystemConfiguration(key='{self.key}', category='{self.category}')>"


# Create indexes for better performance
Index('idx_dtc_code_category', DTCCode.code, DTCCode.category)
Index('idx_vehicle_make_model_year', VehicleProfile.make, VehicleProfile.model, VehicleProfile.year)
Index('idx_session_start_time', DiagnosticSession.start_time)
Index('idx_session_vehicle_time', DiagnosticSession.vehicle_id, DiagnosticSession.start_time)
Index('idx_maintenance_vehicle_date', MaintenanceRecord.vehicle_id, MaintenanceRecord.service_date)
