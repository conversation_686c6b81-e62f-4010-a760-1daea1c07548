// TypeScript type definitions for OBD2 Diagnostic System

export interface ConnectionType {
  id: 'usb' | 'bluetooth' | 'mock';
  name: string;
  description: string;
  icon: string;
  available: boolean;
}

export interface VehicleInfo {
  vin?: string;
  make?: string;
  model?: string;
  year?: number;
  engine?: string;
  fuel_type?: string;
  odometer?: number;
  odometer_unit?: 'km' | 'miles';
  engine_code?: string;
  transmission?: string;
  confidence?: number;
  detection_method?: string;
}

export interface DTCCode {
  code: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  possible_causes?: string[];
  repair_hints?: string[];
  freeze_frame_required?: boolean;
  obd2_standard?: boolean;
  mode?: 'stored' | 'pending' | 'permanent';
  status?: 'active' | 'inactive' | 'intermittent';
  external_links?: {
    google_search?: string;
    tsb_lookup?: string;
    repair_guide?: string;
  };
  fix_guide?: string[];
  estimated_cost?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface OBDParameter {
  pid: string;
  name: string;
  value: number | string;
  unit: string;
  timestamp: string;
  min_value?: number;
  max_value?: number;
  normal_range?: [number, number];
}

export interface DiagnosticSession {
  id: string;
  vehicle_info: VehicleInfo;
  connection_type: string;
  port_used?: string;
  protocol_detected?: string;
  dtc_codes: DTCCode[];
  parameters: OBDParameter[];
  freeze_frame_data?: Record<string, any>;
  ai_analysis?: AIAnalysis;
  status: 'connecting' | 'scanning' | 'analyzing' | 'completed' | 'error';
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

export interface AIAnalysis {
  summary: string;
  recommendations: Recommendation[];
  cost_estimates?: CostEstimate[];
  confidence_score: number;
  model_used: string;
  processing_time: number;
}

export interface Recommendation {
  priority: 'high' | 'medium' | 'low';
  category: 'immediate' | 'scheduled' | 'monitoring';
  title: string;
  description: string;
  estimated_cost?: number;
  estimated_time?: string;
  difficulty: 'easy' | 'moderate' | 'difficult' | 'professional';
}

export interface CostEstimate {
  item: string;
  category: 'parts' | 'labor' | 'diagnostic';
  min_cost: number;
  max_cost: number;
  currency: string;
  notes?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  connection_type?: 'usb' | 'bluetooth' | 'mock';
  port?: string;
  protocol?: string;
  vehicle_info?: VehicleInfo;
  error_message?: string;
  last_activity?: string;
}

export interface ScanProgress {
  stage: 'connecting' | 'reading_dtcs' | 'reading_parameters' | 'ai_analysis' | 'completed';
  progress: number; // 0-100
  message: string;
  estimated_time_remaining?: number;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface DiagnosticHistory {
  sessions: DiagnosticSession[];
  total_sessions: number;
  last_scan?: string;
  common_issues?: string[];
}

// Mock data interfaces for testing
export interface MockScenario {
  id: string;
  name: string;
  description: string;
  vehicle_info: VehicleInfo;
  dtc_codes: DTCCode[];
  parameters: OBDParameter[];
  simulate_delay?: number;
}

// WebSocket message types
export interface WSMessage {
  type: 'connection_status' | 'scan_progress' | 'dtc_found' | 'parameter_update' | 'error';
  data: any;
  timestamp: string;
}

// Form interfaces
export interface ConnectionForm {
  connection_type: 'usb' | 'bluetooth' | 'mock';
  port?: string;
  mock_scenario?: string;
  vehicle_info?: Partial<VehicleInfo>;
}

export interface ScanOptions {
  include_freeze_frame: boolean;
  include_pending_codes: boolean;
  include_permanent_codes: boolean;
  read_live_data: boolean;
  ai_analysis: boolean;
  include_im_readiness: boolean;
  include_vehicle_info: boolean;
  include_odometer: boolean;
}

// I/M Readiness Monitor Status
export interface IMReadinessMonitor {
  name: string;
  status: 'ready' | 'not_ready' | 'not_applicable' | 'error';
  description: string;
  test_available: boolean;
  test_incomplete: boolean;
}

export interface IMReadinessStatus {
  overall_status: 'ready' | 'not_ready' | 'partial';
  ready_count: number;
  total_count: number;
  monitors: IMReadinessMonitor[];
  mil_status: boolean; // Malfunction Indicator Lamp
  dtc_count: number;
  readiness_percentage: number;
}

// Operating Mode
export type OperatingMode = 'safe' | 'expert';

// Notification Types
export interface NotificationConfig {
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  duration?: number;
  sound?: boolean;
  persistent?: boolean;
}

// Confirmation Dialog
export interface ConfirmationDialogConfig {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  requireDoubleConfirm?: boolean;
}
