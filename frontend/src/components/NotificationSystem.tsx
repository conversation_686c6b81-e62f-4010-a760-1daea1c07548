import React, { useState, useEffect, useCallback } from 'react';
import { X, CheckCircle, AlertTriangle, Info, AlertCircle, Volume2, VolumeX } from 'lucide-react';
import { cn } from '../lib/utils';
import { NotificationConfig } from '../types/diagnostic';

interface Notification extends NotificationConfig {
  id: string;
  timestamp: Date;
}

interface NotificationSystemProps {
  enableSound?: boolean;
  onSoundToggle?: (enabled: boolean) => void;
}

export function NotificationSystem({ enableSound = false, onSoundToggle }: NotificationSystemProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [soundEnabled, setSoundEnabled] = useState(enableSound);

  const playNotificationSound = useCallback((type: string) => {
    if (!soundEnabled) return;
    
    try {
      // Create different tones for different notification types
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Different frequencies for different types
      switch (type) {
        case 'error':
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          break;
        case 'warning':
          oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
          break;
        case 'success':
          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
          break;
        default:
          oscillator.frequency.setValueAtTime(500, audioContext.currentTime);
      }
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [soundEnabled]);

  const addNotification = useCallback((config: NotificationConfig) => {
    const notification: Notification = {
      ...config,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };

    setNotifications(prev => [...prev, notification]);

    // Play sound if enabled
    if (config.sound !== false) {
      playNotificationSound(config.type);
    }

    // Auto-remove non-persistent notifications
    if (!config.persistent) {
      setTimeout(() => {
        removeNotification(notification.id);
      }, config.duration || 5000);
    }
  }, [playNotificationSound]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const toggleSound = useCallback(() => {
    const newSoundEnabled = !soundEnabled;
    setSoundEnabled(newSoundEnabled);
    onSoundToggle?.(newSoundEnabled);
  }, [soundEnabled, onSoundToggle]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Info className="w-5 h-5 text-blue-600" />;
    }
  };

  const getNotificationColors = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  // Expose the addNotification function globally
  useEffect(() => {
    (window as any).addNotification = addNotification;
    return () => {
      delete (window as any).addNotification;
    };
  }, [addNotification]);

  return (
    <>
      {/* Sound Control Button */}
      <button
        onClick={toggleSound}
        className={cn(
          'fixed top-4 right-20 z-40 p-2 rounded-full shadow-lg transition-colors',
          soundEnabled ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
        )}
        title={soundEnabled ? 'Disable notification sounds' : 'Enable notification sounds'}
      >
        {soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
      </button>

      {/* Notifications Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={cn(
              'p-4 rounded-lg border shadow-lg transition-all duration-300 transform',
              getNotificationColors(notification.type),
              'animate-in slide-in-from-right-full'
            )}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm">{notification.title}</h4>
                <p className="text-sm mt-1 opacity-90">{notification.message}</p>
                <p className="text-xs mt-2 opacity-70">
                  {notification.timestamp.toLocaleTimeString()}
                </p>
              </div>
              <button
                onClick={() => removeNotification(notification.id)}
                className="flex-shrink-0 text-current opacity-70 hover:opacity-100"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Clear All Button (when multiple notifications) */}
      {notifications.length > 1 && (
        <button
          onClick={clearAllNotifications}
          className="fixed bottom-4 right-4 z-50 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-lg hover:bg-gray-700"
        >
          Clear All ({notifications.length})
        </button>
      )}
    </>
  );
}

// Helper function to add notifications from anywhere in the app
export const addNotification = (config: NotificationConfig) => {
  if ((window as any).addNotification) {
    (window as any).addNotification(config);
  }
};
