import React, { useState, useEffect } from 'react';
import { Car, Gauge, Calendar, Wrench, RefreshCw, Info, ExternalLink } from 'lucide-react';
import { cn } from '../lib/utils';
import { VehicleInfo } from '../types/diagnostic';
import { apiClient } from '../lib/api';

interface VehicleInfoPanelProps {
  vehicleInfo?: VehicleInfo;
  onVehicleInfoUpdate?: (info: VehicleInfo) => void;
  showRefreshButton?: boolean;
}

export function VehicleInfoPanel({ 
  vehicleInfo, 
  onVehicleInfoUpdate, 
  showRefreshButton = true 
}: VehicleInfoPanelProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localVehicleInfo, setLocalVehicleInfo] = useState<VehicleInfo | null>(vehicleInfo || null);

  const refreshVehicleInfo = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.getVehicleInfo();
      if (response.success && response.data) {
        setLocalVehicleInfo(response.data);
        onVehicleInfoUpdate?.(response.data);
      } else {
        setError('Failed to retrieve vehicle information');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const getOdometer = async () => {
    try {
      const response = await apiClient.getOdometer();
      if (response.success && response.data) {
        setLocalVehicleInfo(prev => ({
          ...prev,
          odometer: response.data.value,
          odometer_unit: response.data.unit
        }));
      }
    } catch (err) {
      console.warn('Could not retrieve odometer reading:', err);
    }
  };

  useEffect(() => {
    if (vehicleInfo) {
      setLocalVehicleInfo(vehicleInfo);
    }
  }, [vehicleInfo]);

  useEffect(() => {
    // Auto-refresh vehicle info on mount if not provided
    if (!localVehicleInfo && showRefreshButton) {
      refreshVehicleInfo();
    }
  }, []);

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'text-gray-500';
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return 'Unknown';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };

  const formatVIN = (vin?: string) => {
    if (!vin) return 'Not available';
    // Format VIN with spaces for readability
    return vin.replace(/(.{3})(.{2})(.{6})(.{6})/, '$1 $2 $3 $4');
  };

  const openVINDecoder = () => {
    if (localVehicleInfo?.vin) {
      window.open(`https://www.nhtsa.gov/vin-decoder?vin=${localVehicleInfo.vin}`, '_blank');
    }
  };

  if (!localVehicleInfo && !isLoading) {
    return (
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Car className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Vehicle Information</h3>
          </div>
          {showRefreshButton && (
            <button
              onClick={refreshVehicleInfo}
              className="btn btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Detect Vehicle</span>
            </button>
          )}
        </div>
        <div className="text-center py-8">
          <Car className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No vehicle information available</p>
          <p className="text-sm text-gray-500 mt-2">
            Connect to OBD2 and click "Detect Vehicle" to automatically identify your vehicle
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Car className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Vehicle Information</h3>
        </div>
        {showRefreshButton && (
          <button
            onClick={refreshVehicleInfo}
            disabled={isLoading}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
            <span>{isLoading ? 'Detecting...' : 'Refresh'}</span>
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Detecting vehicle information...</span>
        </div>
      ) : localVehicleInfo ? (
        <div className="space-y-4">
          {/* Basic Vehicle Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Make & Model</label>
                <p className="text-lg font-semibold text-gray-900">
                  {localVehicleInfo.make || 'Unknown'} {localVehicleInfo.model || ''}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Year</label>
                <p className="text-gray-900 flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span>{localVehicleInfo.year || 'Unknown'}</span>
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Engine</label>
                <p className="text-gray-900 flex items-center space-x-2">
                  <Wrench className="w-4 h-4 text-gray-500" />
                  <span>{localVehicleInfo.engine || localVehicleInfo.engine_code || 'Unknown'}</span>
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Fuel Type</label>
                <p className="text-gray-900">{localVehicleInfo.fuel_type || 'Unknown'}</p>
              </div>

              {localVehicleInfo.odometer && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Odometer</label>
                  <p className="text-gray-900 flex items-center space-x-2">
                    <Gauge className="w-4 h-4 text-gray-500" />
                    <span>
                      {localVehicleInfo.odometer.toLocaleString()} {localVehicleInfo.odometer_unit || 'km'}
                    </span>
                  </p>
                  <button
                    onClick={getOdometer}
                    className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                  >
                    Refresh odometer
                  </button>
                </div>
              )}

              {localVehicleInfo.confidence && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Detection Confidence</label>
                  <p className={cn('font-medium', getConfidenceColor(localVehicleInfo.confidence))}>
                    {getConfidenceText(localVehicleInfo.confidence)} ({Math.round(localVehicleInfo.confidence * 100)}%)
                  </p>
                  {localVehicleInfo.detection_method && (
                    <p className="text-xs text-gray-500">Method: {localVehicleInfo.detection_method}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* VIN Information */}
          {localVehicleInfo.vin && (
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">Vehicle Identification Number (VIN)</label>
                <button
                  onClick={openVINDecoder}
                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                >
                  <ExternalLink className="w-3 h-3" />
                  <span>Decode VIN</span>
                </button>
              </div>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded border">
                {formatVIN(localVehicleInfo.vin)}
              </p>
            </div>
          )}

          {/* Additional Info */}
          {localVehicleInfo.transmission && (
            <div className="border-t pt-4">
              <label className="text-sm font-medium text-gray-700">Transmission</label>
              <p className="text-gray-900">{localVehicleInfo.transmission}</p>
            </div>
          )}
        </div>
      ) : null}
    </div>
  );
}
