'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Activity, 
  Gauge, 
  Thermometer, 
  Zap, 
  Fuel, 
  Wind,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Brain,
  Settings,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SensorReading {
  value: number | string;
  unit: string;
  status: 'normal' | 'warning' | 'critical' | 'unknown';
  trend: 'up' | 'down' | 'stable' | 'unknown';
  timestamp: string;
  quality: number;
  analysis?: string;
}

interface FuelMixtureData {
  status: 'optimal' | 'lean' | 'rich' | 'very_lean' | 'very_rich' | 'unknown';
  short_term_trim_bank1: number;
  long_term_trim_bank1: number;
  oxygen_sensor_bank1: number;
  air_fuel_ratio?: number;
  efficiency_rating: number;
  recommendations: string[];
  ai_analysis?: string;
  confidence_score: number;
  timestamp: string;
}

interface LiveDataProps {
  isExpertMode: boolean;
  onExpertModeToggle: (expert: boolean) => void;
}

export function LiveDataDashboard({ isExpertMode, onExpertModeToggle }: LiveDataProps) {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [sensorData, setSensorData] = useState<Record<string, SensorReading>>({});
  const [fuelMixture, setFuelMixture] = useState<FuelMixtureData | null>(null);
  const [updateInterval, setUpdateInterval] = useState(1000); // 1 second
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isMonitoring) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [isMonitoring, updateInterval]);

  const startMonitoring = async () => {
    try {
      // Start live monitoring on backend
      const response = await fetch('/api/v1/enhanced/live-data/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          include_fuel_analysis: true,
          include_ai_analysis: true,
          update_interval: updateInterval / 1000
        })
      });

      if (!response.ok) {
        throw new Error('Failed to start monitoring');
      }

      // Start polling for data
      intervalRef.current = setInterval(fetchLiveData, updateInterval);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start monitoring');
      setIsMonitoring(false);
    }
  };

  const stopMonitoring = async () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    try {
      await fetch('/api/v1/enhanced/live-data/stop', { method: 'POST' });
    } catch (err) {
      console.error('Error stopping monitoring:', err);
    }
  };

  const fetchLiveData = async () => {
    try {
      const response = await fetch('/api/v1/enhanced/live-data/current');
      if (!response.ok) {
        throw new Error('Failed to fetch live data');
      }

      const result = await response.json();
      if (result.success) {
        setSensorData(result.data.sensors || {});
        setFuelMixture(result.data.fuel_mixture);
        setError(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    }
  };

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };

  const resetData = () => {
    setSensorData({});
    setFuelMixture(null);
    setError(null);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-blue-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-blue-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const getMixtureStatusColor = (status: string) => {
    switch (status) {
      case 'optimal':
        return 'text-green-600 bg-green-50';
      case 'lean':
      case 'rich':
        return 'text-yellow-600 bg-yellow-50';
      case 'very_lean':
      case 'very_rich':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatValue = (value: number | string, unit: string) => {
    if (typeof value === 'number') {
      return `${value.toFixed(1)} ${unit}`;
    }
    return `${value} ${unit}`;
  };

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Live Data Monitor</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onExpertModeToggle(!isExpertMode)}
              className={cn(
                'px-3 py-1 rounded-full text-sm font-medium',
                isExpertMode
                  ? 'bg-red-100 text-red-700 border border-red-200'
                  : 'bg-gray-100 text-gray-700 border border-gray-200'
              )}
            >
              <Settings className="w-4 h-4 inline mr-1" />
              {isExpertMode ? 'Expert Mode' : 'Normal Mode'}
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <button
            onClick={toggleMonitoring}
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg font-medium',
              isMonitoring
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            )}
          >
            {isMonitoring ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            <span>{isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}</span>
          </button>

          <button
            onClick={resetData}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset</span>
          </button>

          <select
            value={updateInterval}
            onChange={(e) => setUpdateInterval(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            disabled={isMonitoring}
          >
            <option value={500}>0.5s</option>
            <option value={1000}>1s</option>
            <option value={2000}>2s</option>
            <option value={5000}>5s</option>
          </select>

          {isMonitoring && (
            <div className="flex items-center space-x-2 text-sm text-green-600">
              <Activity className="w-4 h-4 animate-pulse" />
              <span>Live</span>
            </div>
          )}
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Fuel Mixture Analysis */}
      {fuelMixture && (
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Fuel className="w-5 h-5 mr-2" />
            Fuel Mixture Analysis
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className={cn('px-3 py-1 rounded-full text-sm font-medium mb-2', getMixtureStatusColor(fuelMixture.status))}>
                {fuelMixture.status.toUpperCase()}
              </div>
              <p className="text-xs text-gray-500">Mixture Status</p>
            </div>

            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {fuelMixture.short_term_trim_bank1.toFixed(1)}%
              </div>
              <p className="text-xs text-gray-500">Short Term Trim</p>
            </div>

            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {fuelMixture.long_term_trim_bank1.toFixed(1)}%
              </div>
              <p className="text-xs text-gray-500">Long Term Trim</p>
            </div>

            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {fuelMixture.efficiency_rating.toFixed(0)}%
              </div>
              <p className="text-xs text-gray-500">Efficiency</p>
            </div>
          </div>

          {fuelMixture.air_fuel_ratio && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Air-Fuel Ratio:</strong> {fuelMixture.air_fuel_ratio.toFixed(2)}:1
                {fuelMixture.air_fuel_ratio > 14.7 ? ' (Lean)' : fuelMixture.air_fuel_ratio < 14.7 ? ' (Rich)' : ' (Stoichiometric)'}
              </p>
            </div>
          )}

          {fuelMixture.ai_analysis && (
            <div className="mb-4 p-3 bg-purple-50 rounded-lg">
              <div className="flex items-start space-x-2">
                <Brain className="w-4 h-4 text-purple-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-purple-800">AI Analysis</p>
                  <p className="text-sm text-purple-700">{fuelMixture.ai_analysis}</p>
                </div>
              </div>
            </div>
          )}

          {fuelMixture.recommendations.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Recommendations</h4>
              <ul className="space-y-1">
                {fuelMixture.recommendations.map((rec, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Sensor Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {Object.entries(sensorData).map(([sensorName, reading]) => (
          <SensorCard
            key={sensorName}
            name={sensorName}
            reading={reading}
            isExpertMode={isExpertMode}
          />
        ))}
      </div>

      {Object.keys(sensorData).length === 0 && !isMonitoring && (
        <div className="text-center py-12">
          <Gauge className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Start monitoring to see live sensor data</p>
        </div>
      )}
    </div>
  );
}

interface SensorCardProps {
  name: string;
  reading: SensorReading;
  isExpertMode: boolean;
}

function SensorCard({ name, reading, isExpertMode }: SensorCardProps) {
  const getSensorIcon = (name: string) => {
    if (name.includes('TEMP')) return <Thermometer className="w-5 h-5" />;
    if (name.includes('RPM')) return <Gauge className="w-5 h-5" />;
    if (name.includes('SPEED')) return <Wind className="w-5 h-5" />;
    if (name.includes('FUEL') || name.includes('OXYGEN')) return <Fuel className="w-5 h-5" />;
    if (name.includes('VOLTAGE') || name.includes('CURRENT')) return <Zap className="w-5 h-5" />;
    return <Activity className="w-5 h-5" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'border-green-200 bg-green-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'critical': return 'border-red-200 bg-red-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-blue-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-blue-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatValue = (value: number | string, unit: string) => {
    if (typeof value === 'number') {
      return `${value.toFixed(1)} ${unit}`;
    }
    return `${value} ${unit}`;
  };

  return (
    <div className={cn('bg-white rounded-lg shadow-sm border-2 p-4', getStatusColor(reading.status))}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {getSensorIcon(name)}
          <h3 className="text-sm font-medium text-gray-900 truncate">
            {name.replace(/_/g, ' ')}
          </h3>
        </div>
        <div className="flex items-center space-x-1">
          {getStatusIcon(reading.status)}
          {getTrendIcon(reading.trend)}
        </div>
      </div>

      <div className="mb-2">
        <div className="text-2xl font-bold text-gray-900">
          {formatValue(reading.value, reading.unit)}
        </div>
        {isExpertMode && (
          <div className="text-xs text-gray-500">
            Quality: {(reading.quality * 100).toFixed(0)}%
          </div>
        )}
      </div>

      {reading.analysis && (
        <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-800">
          {reading.analysis}
        </div>
      )}

      <div className="text-xs text-gray-400 mt-2">
        {new Date(reading.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
}
