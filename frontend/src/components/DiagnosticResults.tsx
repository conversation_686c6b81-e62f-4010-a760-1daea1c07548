'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle, 
  Clock, 
  Car, 
  Wrench, 
  DollarSign,
  Download,
  Refresh<PERSON><PERSON>,
  <PERSON>,
  TrendingUp
} from 'lucide-react';
import { DiagnosticSession, DTCCode, Recommendation } from '@/types/diagnostic';
import { LoadingSpinner, <PERSON><PERSON>ingAnimation } from './LoadingSpinner';
import { DTCAnalysis } from './DTCAnalysis';
import { cn, formatDate, formatCurrency, getSeverityColor } from '@/lib/utils';

interface DiagnosticResultsProps {
  session: DiagnosticSession;
  onNewScan: () => void;
  onDisconnect: () => void;
}

export function DiagnosticResults({ 
  session, 
  onNewScan, 
  onDisconnect 
}: DiagnosticResultsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'dtc' | 'parameters' | 'ai'>('overview');

  const getSeverityStats = () => {
    const stats = { critical: 0, high: 0, medium: 0, low: 0 };
    session.dtc_codes.forEach(code => {
      stats[code.severity]++;
    });
    return stats;
  };

  const getStatusIcon = () => {
    switch (session.status) {
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-600" />;
      case 'error':
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
      default:
        return <Clock className="w-6 h-6 text-blue-600" />;
    }
  };

  const renderOverview = () => {
    const severityStats = getSeverityStats();
    const totalCodes = session.dtc_codes.length;

    return (
      <div className="space-y-6">
        {/* Status Summary */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Diagnostic Summary</h3>
            {getStatusIcon()}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{totalCodes}</div>
              <div className="text-sm text-gray-600">Total Issues</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{session.parameters.length}</div>
              <div className="text-sm text-gray-600">Parameters Read</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">
                {session.ai_analysis?.confidence_score ? 
                  `${Math.round(session.ai_analysis.confidence_score * 100)}%` : 
                  'N/A'
                }
              </div>
              <div className="text-sm text-gray-600">AI Confidence</div>
            </div>
          </div>
        </div>

        {/* Severity Breakdown */}
        {totalCodes > 0 && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Issue Severity</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(severityStats).map(([severity, count]) => (
                <div key={severity} className="text-center">
                  <div className={cn(
                    'text-2xl font-bold mb-1',
                    severity === 'critical' && 'text-red-600',
                    severity === 'high' && 'text-orange-600',
                    severity === 'medium' && 'text-yellow-600',
                    severity === 'low' && 'text-green-600'
                  )}>
                    {count}
                  </div>
                  <div className="text-sm text-gray-600 capitalize">{severity}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Vehicle Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {session.vehicle_info.make && (
              <div>
                <span className="text-sm text-gray-600">Make:</span>
                <span className="ml-2 font-medium">{session.vehicle_info.make}</span>
              </div>
            )}
            {session.vehicle_info.model && (
              <div>
                <span className="text-sm text-gray-600">Model:</span>
                <span className="ml-2 font-medium">{session.vehicle_info.model}</span>
              </div>
            )}
            {session.vehicle_info.year && (
              <div>
                <span className="text-sm text-gray-600">Year:</span>
                <span className="ml-2 font-medium">{session.vehicle_info.year}</span>
              </div>
            )}
            {session.vehicle_info.vin && (
              <div>
                <span className="text-sm text-gray-600">VIN:</span>
                <span className="ml-2 font-medium font-mono text-xs">{session.vehicle_info.vin}</span>
              </div>
            )}
            <div>
              <span className="text-sm text-gray-600">Connection:</span>
              <span className="ml-2 font-medium capitalize">{session.connection_type}</span>
            </div>
            {session.protocol_detected && (
              <div>
                <span className="text-sm text-gray-600">Protocol:</span>
                <span className="ml-2 font-medium">{session.protocol_detected}</span>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="flex flex-wrap gap-3">
            <button onClick={onNewScan} className="btn-primary">
              <RefreshCw className="w-4 h-4 mr-2" />
              New Scan
            </button>
            <button onClick={onDisconnect} className="btn-secondary">
              <Car className="w-4 h-4 mr-2" />
              Disconnect
            </button>
            <button className="btn-secondary">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderDTCCodes = () => {
    return (
      <DTCAnalysis
        dtcCodes={session.dtc_codes}
        vehicleInfo={session.vehicle_info}
        onAnalysisComplete={(analysis) => {
          console.log('AI Analysis completed:', analysis);
        }}
      />
    );
  };

  const renderParameters = () => {
    if (session.parameters.length === 0) {
      return (
        <div className="card text-center py-12">
          <Wrench className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Parameters Read</h3>
          <p className="text-gray-600">No OBD parameters were read during this scan.</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {session.parameters.map((param, index) => (
            <div key={index} className="card">
              <h4 className="font-semibold text-gray-900 mb-2">{param.name}</h4>
              <div className="flex items-baseline space-x-2">
                <span className="text-2xl font-bold text-blue-600">{param.value}</span>
                <span className="text-sm text-gray-600">{param.unit}</span>
              </div>
              <div className="text-xs text-gray-500 mt-1">PID: {param.pid}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderAIAnalysis = () => {
    if (!session.ai_analysis) {
      return (
        <div className="card text-center py-12">
          <Brain className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No AI Analysis Available</h3>
          <p className="text-gray-600">AI analysis was not performed for this scan.</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* AI Summary */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">AI Analysis Summary</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>Confidence: {Math.round(session.ai_analysis.confidence_score * 100)}%</span>
              <span>Model: {session.ai_analysis.model_used}</span>
              <span>Time: {session.ai_analysis.processing_time.toFixed(1)}s</span>
            </div>
          </div>
          <div className="prose max-w-none">
            <p className="text-gray-700 leading-relaxed">{session.ai_analysis.summary}</p>
          </div>
        </div>

        {/* AI Recommendations */}
        {session.ai_analysis.recommendations && session.ai_analysis.recommendations.length > 0 && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Recommendations</h3>
            <div className="space-y-4">
              {session.ai_analysis.recommendations.map((rec, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{rec.title}</h4>
                    <span className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium',
                      rec.priority === 'high' && 'bg-red-100 text-red-800',
                      rec.priority === 'medium' && 'bg-yellow-100 text-yellow-800',
                      rec.priority === 'low' && 'bg-green-100 text-green-800'
                    )}>
                      {rec.priority.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-3">{rec.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    {rec.estimated_cost && (
                      <span>Cost: ${rec.estimated_cost}</span>
                    )}
                    {rec.estimated_time && (
                      <span>Time: {rec.estimated_time}</span>
                    )}
                    {rec.difficulty && (
                      <span>Difficulty: {rec.difficulty}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Diagnostic Results</h1>
          <div className="text-sm text-gray-600">
            {formatDate(session.created_at)}
          </div>
        </div>
        
        {/* Status Bar */}
        <div className={cn(
          'px-4 py-2 rounded-lg flex items-center space-x-2',
          session.status === 'completed' && 'bg-green-50 text-green-700',
          session.status === 'error' && 'bg-red-50 text-red-700',
          ['connecting', 'scanning', 'analyzing'].includes(session.status) && 'bg-blue-50 text-blue-700'
        )}>
          {getStatusIcon()}
          <span className="font-medium capitalize">
            {session.status === 'completed' ? 'Scan Complete' : session.status}
          </span>
          {session.error_message && (
            <span className="text-sm">- {session.error_message}</span>
          )}
        </div>
      </div>

      {/* Show scanning animation if still in progress */}
      {['connecting', 'scanning', 'analyzing'].includes(session.status) && (
        <div className="card text-center py-12">
          <ScanningAnimation 
            stage={session.status}
            progress={session.status === 'connecting' ? 25 : session.status === 'scanning' ? 60 : 85}
            message={`${session.status === 'connecting' ? 'Establishing connection...' : 
                     session.status === 'scanning' ? 'Reading diagnostic codes...' : 
                     'Analyzing results with AI...'}`}
          />
        </div>
      )}

      {/* Results Content */}
      {session.status === 'completed' && (
        <>
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: 'Overview', icon: TrendingUp },
                { id: 'dtc', name: 'Trouble Codes', icon: AlertTriangle },
                { id: 'parameters', name: 'Parameters', icon: Wrench },
                { id: 'ai', name: 'AI Analysis', icon: Brain },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={cn(
                    'flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm',
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'dtc' && renderDTCCodes()}
          {activeTab === 'parameters' && renderParameters()}
          {activeTab === 'ai' && renderAIAnalysis()}
        </>
      )}
    </div>
  );
}
