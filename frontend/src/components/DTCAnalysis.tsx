import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Clock, CheckCircle, Loader2,
  Search, ExternalLink, Trash2, Eye, HelpCircle, Shield,
  ShieldCheck, Car, RefreshCw, Settings, Volume2, VolumeX,
  Download, Upload, RotateCcw, Zap
} from 'lucide-react';
import { cn } from '../lib/utils';
import { DTCCode, VehicleInfo, OperatingMode, IMReadinessStatus, ConfirmationDialogConfig } from '../types/diagnostic';
import { apiClient } from '../lib/api';
import { VehicleInfoPanel } from './VehicleInfoPanel';
import { IMReadinessPanel } from './IMReadinessPanel';
import { ConfirmationDialog } from './ConfirmationDialog';
import { NotificationSystem, addNotification } from './NotificationSystem';
import { FreezeFramePanel } from './FreezeFramePanel';
import { ServiceLightReset } from './ServiceLightReset';
import { ECUInfoPanel } from './ECUInfoPanel';

interface AIAnalysis {
  analysis: string;
  confidence_score: number;
  model_used: string;
  processing_time: number;
  dtc_codes: string[];
  repair_recommendations?: string[];
  cost_estimates?: {
    min: number;
    max: number;
    currency: string;
  };
  predictive_maintenance?: string[];
}

interface DTCAnalysisProps {
  dtcCodes: DTCCode[];
  vehicleInfo?: VehicleInfo;
  onAnalysisComplete?: (analysis: AIAnalysis) => void;
  operatingMode?: OperatingMode;
  onModeChange?: (mode: OperatingMode) => void;
}

export function DTCAnalysis({
  dtcCodes,
  vehicleInfo,
  onAnalysisComplete,
  operatingMode = 'safe',
  onModeChange
}: DTCAnalysisProps) {
  // Core state
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDTC, setSelectedDTC] = useState<string | null>(null);

  // Enhanced state
  const [allDTCs, setAllDTCs] = useState<{
    stored: DTCCode[];
    pending: DTCCode[];
    permanent: DTCCode[];
  }>({ stored: dtcCodes, pending: [], permanent: [] });
  const [activeMode, setActiveMode] = useState<'stored' | 'pending' | 'permanent'>('stored');
  const [isLoadingDTCs, setIsLoadingDTCs] = useState(false);
  const [imReadinessStatus, setIMReadinessStatus] = useState<IMReadinessStatus | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    config: ConfirmationDialogConfig;
    onConfirm: () => void;
  }>({
    isOpen: false,
    config: { title: '', message: '' },
    onConfirm: () => {}
  });

  // Load DTCs by mode
  const loadDTCsByMode = useCallback(async (mode: 'stored' | 'pending' | 'permanent') => {
    setIsLoadingDTCs(true);
    try {
      const response = await apiClient.getDTCsByMode(mode);
      if (response.success && response.data) {
        setAllDTCs(prev => ({
          ...prev,
          [mode]: response.data
        }));

        addNotification({
          type: 'success',
          title: 'DTCs Loaded',
          message: `${response.data.length} ${mode} DTCs retrieved`,
          duration: 3000
        });
      }
    } catch (err) {
      addNotification({
        type: 'error',
        title: 'Failed to Load DTCs',
        message: err instanceof Error ? err.message : 'Unknown error',
        duration: 5000
      });
    } finally {
      setIsLoadingDTCs(false);
    }
  }, []);

  // Clear DTCs with confirmation
  const clearDTCs = useCallback(async (mode: string = 'all') => {
    const config: ConfirmationDialogConfig = {
      title: 'Clear Diagnostic Trouble Codes',
      message: `Are you sure you want to clear ${mode === 'all' ? 'all' : mode} DTCs? This action cannot be undone and may affect vehicle diagnostics.`,
      confirmText: 'Clear DTCs',
      cancelText: 'Cancel',
      severity: 'high',
      requireDoubleConfirm: true
    };

    setConfirmationDialog({
      isOpen: true,
      config,
      onConfirm: async () => {
        try {
          const response = await apiClient.clearDTCs(true, mode);
          if (response.success) {
            // Refresh DTCs after clearing
            await loadDTCsByMode('stored');
            await loadDTCsByMode('pending');
            await loadDTCsByMode('permanent');

            addNotification({
              type: 'success',
              title: 'DTCs Cleared',
              message: `${mode === 'all' ? 'All' : mode} DTCs have been cleared successfully`,
              duration: 5000,
              sound: true
            });
          }
        } catch (err) {
          addNotification({
            type: 'error',
            title: 'Failed to Clear DTCs',
            message: err instanceof Error ? err.message : 'Unknown error',
            duration: 5000,
            sound: true
          });
        }
        setConfirmationDialog(prev => ({ ...prev, isOpen: false }));
      }
    });
  }, [loadDTCsByMode]);

  // Clear single DTC
  const clearSingleDTC = useCallback(async (code: string) => {
    const config: ConfirmationDialogConfig = {
      title: 'Clear Single DTC',
      message: `Clear diagnostic trouble code ${code}? This action cannot be undone.`,
      confirmText: 'Clear Code',
      cancelText: 'Cancel',
      severity: 'medium',
      requireDoubleConfirm: false
    };

    setConfirmationDialog({
      isOpen: true,
      config,
      onConfirm: async () => {
        try {
          const response = await apiClient.clearSingleDTC(code, true);
          if (response.success) {
            // Refresh current mode DTCs
            await loadDTCsByMode(activeMode);

            addNotification({
              type: 'success',
              title: 'DTC Cleared',
              message: `Code ${code} has been cleared`,
              duration: 3000
            });
          }
        } catch (err) {
          addNotification({
            type: 'error',
            title: 'Failed to Clear DTC',
            message: err instanceof Error ? err.message : 'Unknown error',
            duration: 5000
          });
        }
        setConfirmationDialog(prev => ({ ...prev, isOpen: false }));
      }
    });
  }, [activeMode, loadDTCsByMode]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-orange-600" />;
      case 'medium':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-600" />;
    }
  };

  // Enhanced AI analysis with pattern recognition
  const analyzeWithAI = async (codes?: string[], includePatternAnalysis: boolean = true) => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const currentDTCs = allDTCs[activeMode];
      const codesToAnalyze = codes || currentDTCs.map(dtc => dtc.code);

      if (codesToAnalyze.length === 0) {
        addNotification({
          type: 'warning',
          title: 'No DTCs to Analyze',
          message: 'No diagnostic trouble codes found for analysis',
          duration: 3000
        });
        return;
      }

      const response = await fetch('/api/v1/frontend/dtc/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dtc_codes: codesToAnalyze,
          vehicle_info: vehicleInfo || {},
          include_pattern_analysis: includePatternAnalysis,
          include_cost_estimates: true,
          include_predictive_maintenance: true
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAiAnalysis(result.data);
        onAnalysisComplete?.(result.data);

        addNotification({
          type: 'success',
          title: 'AI Analysis Complete',
          message: `Analysis completed with ${Math.round(result.data.confidence_score * 100)}% confidence`,
          duration: 5000
        });
      } else {
        setError(result.error || 'AI analysis failed');
        addNotification({
          type: 'error',
          title: 'AI Analysis Failed',
          message: result.error || 'Unknown error occurred',
          duration: 5000
        });
      }
    } catch (err) {
      const errorMessage = 'Failed to connect to AI analysis service';
      setError(errorMessage);
      addNotification({
        type: 'error',
        title: 'Connection Error',
        message: errorMessage,
        duration: 5000
      });
      console.error('AI analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate external links for DTCs
  const generateExternalLinks = (code: string) => {
    return {
      google_search: `https://www.google.com/search?q=${encodeURIComponent(`${code} diagnostic trouble code repair`)}`,
      tsb_lookup: `https://www.nhtsa.gov/recalls?vin=${vehicleInfo?.vin || ''}&make=${vehicleInfo?.make || ''}&model=${vehicleInfo?.model || ''}&year=${vehicleInfo?.year || ''}`,
      repair_guide: `https://www.repairpal.com/obd-ii-trouble-codes/${code.toLowerCase()}`
    };
  };

  // Toggle operating mode
  const toggleOperatingMode = () => {
    const newMode = operatingMode === 'safe' ? 'expert' : 'safe';

    if (newMode === 'expert') {
      const config: ConfirmationDialogConfig = {
        title: 'Activate Expert Mode',
        message: 'Expert Mode enables write operations that can modify vehicle settings. Only proceed if you understand the risks. Incorrect operations may damage your vehicle.',
        confirmText: 'Activate Expert Mode',
        cancelText: 'Stay in Safe Mode',
        severity: 'critical',
        requireDoubleConfirm: true
      };

      setConfirmationDialog({
        isOpen: true,
        config,
        onConfirm: () => {
          onModeChange?.(newMode);
          addNotification({
            type: 'warning',
            title: 'Expert Mode Activated',
            message: 'Write operations are now enabled. Use with caution.',
            duration: 5000,
            sound: true
          });
          setConfirmationDialog(prev => ({ ...prev, isOpen: false }));
        }
      });
    } else {
      onModeChange?.(newMode);
      addNotification({
        type: 'info',
        title: 'Safe Mode Activated',
        message: 'Only read operations are allowed',
        duration: 3000
      });
    }
  };

  // Initialize data on mount
  useEffect(() => {
    setAllDTCs(prev => ({ ...prev, stored: dtcCodes }));
  }, [dtcCodes]);

  // Load additional DTC modes on mount
  useEffect(() => {
    loadDTCsByMode('pending');
    loadDTCsByMode('permanent');
  }, [loadDTCsByMode]);

  const formatAnalysisText = (text: string) => {
    // Split by common delimiters and format as structured content
    const sections = text.split(/\n\n|\n(?=[A-Z][a-z]+:)/).filter(Boolean);

    return sections.map((section, index) => {
      if (section.includes(':')) {
        const [title, ...content] = section.split(':');
        return (
          <div key={index} className="mb-4">
            <h4 className="font-semibold text-gray-900 mb-2">{title.trim()}:</h4>
            <p className="text-gray-700 leading-relaxed">{content.join(':').trim()}</p>
          </div>
        );
      }
      return (
        <p key={index} className="text-gray-700 leading-relaxed mb-3">
          {section.trim()}
        </p>
      );
    });
  };

  // Get current DTCs based on active mode
  const getCurrentDTCs = () => allDTCs[activeMode];
  const currentDTCs = getCurrentDTCs();

  // Calculate DTC statistics
  const dtcStats = {
    total: Object.values(allDTCs).flat().length,
    stored: allDTCs.stored.length,
    pending: allDTCs.pending.length,
    permanent: allDTCs.permanent.length,
    critical: currentDTCs.filter(dtc => dtc.severity === 'critical').length,
    high: currentDTCs.filter(dtc => dtc.severity === 'high').length
  };

  return (
    <div className="space-y-6">
      {/* Notification System */}
      <NotificationSystem
        enableSound={soundEnabled}
        onSoundToggle={setSoundEnabled}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        config={confirmationDialog.config}
        onConfirm={confirmationDialog.onConfirm}
        onCancel={() => setConfirmationDialog(prev => ({ ...prev, isOpen: false }))}
      />

      {/* Header with Operating Mode Toggle */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900">Diagnostic Analysis</h2>
            <div className={cn(
              'px-3 py-1 rounded-full text-sm font-medium border',
              operatingMode === 'expert'
                ? 'bg-red-100 text-red-800 border-red-200'
                : 'bg-green-100 text-green-800 border-green-200'
            )}>
              {operatingMode === 'expert' ? (
                <>
                  <Shield className="w-3 h-3 inline mr-1" />
                  Expert Mode
                </>
              ) : (
                <>
                  <ShieldCheck className="w-3 h-3 inline mr-1" />
                  Safe Mode
                </>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
              title="Help & Information"
            >
              <HelpCircle className="w-5 h-5" />
            </button>

            <button
              onClick={toggleOperatingMode}
              className={cn(
                'px-4 py-2 rounded-lg font-medium transition-colors',
                operatingMode === 'expert'
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-red-600 text-white hover:bg-red-700'
              )}
            >
              {operatingMode === 'expert' ? 'Switch to Safe Mode' : 'Switch to Expert Mode'}
            </button>
          </div>
        </div>

        {/* Help Panel */}
        {showHelp && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h4 className="font-semibold text-blue-900 mb-2">Diagnostic Analysis Help</h4>
            <div className="text-sm text-blue-800 space-y-2">
              <p><strong>Safe Mode:</strong> Read-only operations. View DTCs, run diagnostics, and get AI analysis.</p>
              <p><strong>Expert Mode:</strong> Enables write operations like clearing DTCs and service resets. Use with caution.</p>
              <p><strong>DTC Modes:</strong> Stored (active codes), Pending (intermittent), Permanent (emissions-related).</p>
              <p><strong>I/M Readiness:</strong> Shows emission monitor status for inspection/maintenance compliance.</p>
            </div>
          </div>
        )}

        {/* DTC Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{dtcStats.total}</p>
            <p className="text-sm text-gray-600">Total DTCs</p>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">{dtcStats.stored}</p>
            <p className="text-sm text-gray-600">Stored</p>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <p className="text-2xl font-bold text-yellow-600">{dtcStats.pending}</p>
            <p className="text-sm text-gray-600">Pending</p>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <p className="text-2xl font-bold text-red-600">{dtcStats.critical + dtcStats.high}</p>
            <p className="text-sm text-gray-600">Critical/High</p>
          </div>
        </div>
      </div>

      {/* Vehicle Information Panel */}
      <VehicleInfoPanel
        vehicleInfo={vehicleInfo}
        onVehicleInfoUpdate={(info) => {
          // Handle vehicle info updates
          addNotification({
            type: 'success',
            title: 'Vehicle Info Updated',
            message: `Detected: ${info.make} ${info.model} (${info.year})`,
            duration: 3000
          });
        }}
      />

      {/* I/M Readiness Panel */}
      <IMReadinessPanel
        onStatusUpdate={setIMReadinessStatus}
        autoRefresh={true}
        refreshInterval={30}
      />

      {/* AI Analysis Section */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI-Powered Analysis</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => analyzeWithAI(undefined, false)}
              disabled={isAnalyzing || currentDTCs.length === 0}
              className="btn btn-secondary flex items-center space-x-2"
            >
              {isAnalyzing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Brain className="w-4 h-4" />
              )}
              <span>{isAnalyzing ? 'Analyzing...' : 'Quick Analysis'}</span>
            </button>
            <button
              onClick={() => analyzeWithAI(undefined, true)}
              disabled={isAnalyzing || currentDTCs.length === 0}
              className="btn btn-primary flex items-center space-x-2"
            >
              {isAnalyzing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Zap className="w-4 h-4" />
              )}
              <span>{isAnalyzing ? 'Analyzing...' : 'Deep Analysis'}</span>
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {aiAnalysis && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-blue-900">AI Analysis Results</h4>
              <div className="flex items-center space-x-4 text-sm text-blue-700">
                <span>Confidence: {Math.round(aiAnalysis.confidence_score * 100)}%</span>
                <span>Model: {aiAnalysis.model_used}</span>
                <span>Time: {aiAnalysis.processing_time.toFixed(1)}s</span>
              </div>
            </div>

            <div className="prose prose-blue max-w-none mb-6">
              {formatAnalysisText(aiAnalysis.analysis)}
            </div>

            {/* Enhanced AI Results */}
            {aiAnalysis.repair_recommendations && aiAnalysis.repair_recommendations.length > 0 && (
              <div className="mb-4">
                <h5 className="font-semibold text-blue-900 mb-2">Repair Recommendations</h5>
                <ul className="list-disc list-inside text-sm text-blue-800 space-y-1">
                  {aiAnalysis.repair_recommendations.map((rec, i) => (
                    <li key={i}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}

            {aiAnalysis.cost_estimates && (
              <div className="mb-4">
                <h5 className="font-semibold text-blue-900 mb-2">Estimated Repair Cost</h5>
                <p className="text-blue-800">
                  ${aiAnalysis.cost_estimates.min} - ${aiAnalysis.cost_estimates.max} {aiAnalysis.cost_estimates.currency}
                </p>
              </div>
            )}

            {aiAnalysis.predictive_maintenance && aiAnalysis.predictive_maintenance.length > 0 && (
              <div>
                <h5 className="font-semibold text-blue-900 mb-2">Predictive Maintenance</h5>
                <ul className="list-disc list-inside text-sm text-blue-800 space-y-1">
                  {aiAnalysis.predictive_maintenance.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {currentDTCs.length === 0 && (
          <div className="text-center py-8">
            <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No DTCs available for analysis</p>
            <p className="text-sm text-gray-500 mt-2">
              Scan for diagnostic trouble codes to enable AI analysis
            </p>
          </div>
        )}
      </div>

      {/* DTC Management Section */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Diagnostic Trouble Codes</h3>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => loadDTCsByMode(activeMode)}
              disabled={isLoadingDTCs}
              className="btn btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className={cn('w-4 h-4', isLoadingDTCs && 'animate-spin')} />
              <span>Refresh</span>
            </button>

            {operatingMode === 'expert' && currentDTCs.length > 0 && (
              <button
                onClick={() => clearDTCs('all')}
                className="btn bg-red-600 text-white hover:bg-red-700 flex items-center space-x-2"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear All DTCs</span>
              </button>
            )}
          </div>
        </div>

        {/* DTC Mode Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          {(['stored', 'pending', 'permanent'] as const).map((mode) => (
            <button
              key={mode}
              onClick={() => setActiveMode(mode)}
              className={cn(
                'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors',
                activeMode === mode
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              )}
            >
              <span className="capitalize">{mode}</span>
              <span className="ml-2 px-2 py-0.5 text-xs bg-gray-200 rounded-full">
                {allDTCs[mode].length}
              </span>
            </button>
          ))}
        </div>

        {/* DTC List */}
        {currentDTCs.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              No {activeMode} DTCs Found
            </h4>
            <p className="text-gray-600">
              {activeMode === 'stored'
                ? 'Your vehicle appears to be running normally with no active diagnostic trouble codes.'
                : `No ${activeMode} diagnostic trouble codes detected.`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {currentDTCs.map((code, index) => (
              <div key={`${code.code}-${index}`} className="card border-l-4 border-l-blue-500">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      'px-3 py-1 rounded-full text-sm font-medium border',
                      getSeverityColor(code.severity)
                    )}>
                      {code.severity.toUpperCase()}
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">{code.code}</h4>
                    {code.mode && (
                      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                        {code.mode}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {getSeverityIcon(code.severity)}
                  </div>
                </div>

                <p className="text-gray-700 mb-4">{code.description}</p>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2 mb-4">
                  <button
                    onClick={() => analyzeWithAI([code.code])}
                    disabled={isAnalyzing}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center space-x-1"
                  >
                    <Brain className="w-3 h-3" />
                    <span>AI Analyze</span>
                  </button>

                  <button
                    onClick={() => setSelectedDTC(selectedDTC === code.code ? null : code.code)}
                    className="text-gray-600 hover:text-gray-800 text-sm flex items-center space-x-1"
                  >
                    <Eye className="w-3 h-3" />
                    <span>{selectedDTC === code.code ? 'Hide Details' : 'View Details'}</span>
                  </button>

                  <a
                    href={generateExternalLinks(code.code).google_search}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-green-600 hover:text-green-800 text-sm flex items-center space-x-1"
                  >
                    <Search className="w-3 h-3" />
                    <span>Google</span>
                  </a>

                  <a
                    href={generateExternalLinks(code.code).repair_guide}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-purple-600 hover:text-purple-800 text-sm flex items-center space-x-1"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>Repair Guide</span>
                  </a>

                  {operatingMode === 'expert' && (
                    <button
                      onClick={() => clearSingleDTC(code.code)}
                      className="text-red-600 hover:text-red-800 text-sm flex items-center space-x-1"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>Clear</span>
                    </button>
                  )}
                </div>

                {/* Expanded Details */}
                {selectedDTC === code.code && (
                  <div className="border-t pt-4 space-y-4">
                    {code.possible_causes && code.possible_causes.length > 0 && (
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                          <AlertTriangle className="w-4 h-4" />
                          <span>Possible Causes:</span>
                        </h5>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {code.possible_causes.map((cause, i) => (
                            <li key={i}>{cause}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {code.repair_hints && code.repair_hints.length > 0 && (
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                          <Wrench className="w-4 h-4" />
                          <span>Repair Recommendations:</span>
                        </h5>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {code.repair_hints.map((hint, i) => (
                            <li key={i}>{hint}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {code.fix_guide && code.fix_guide.length > 0 && (
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                          <Settings className="w-4 h-4" />
                          <span>Step-by-Step Fix Guide:</span>
                        </h5>
                        <ol className="list-decimal list-inside text-sm text-gray-600 space-y-1">
                          {code.fix_guide.map((step, i) => (
                            <li key={i}>{step}</li>
                          ))}
                        </ol>
                      </div>
                    )}

                    {code.estimated_cost && (
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Estimated Repair Cost:</h5>
                        <p className="text-sm text-gray-600">
                          ${code.estimated_cost.min} - ${code.estimated_cost.max} {code.estimated_cost.currency}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
