'use client';

import { useState, useEffect } from 'react';
import { 
  Snowf<PERSON>, 
  Clock, 
  Thermometer, 
  Gauge, 
  Fuel, 
  Wind,
  RefreshCw,
  Download,
  Eye,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FreezeFrameData {
  dtc_code: string;
  timestamp: string;
  engine_speed: number;
  vehicle_speed: number;
  engine_coolant_temp: number;
  intake_air_temp: number;
  manifold_pressure: number;
  throttle_position: number;
  fuel_trim_bank1: number;
  fuel_trim_bank2: number;
  oxygen_sensor_voltage: number;
  engine_load: number;
  fuel_pressure: number;
  ignition_timing: number;
  air_flow_rate: number;
}

interface FreezeFramePanelProps {
  dtcCode?: string;
  onDataLoad?: (data: FreezeFrameData[]) => void;
}

export function FreezeFramePanel({ dtcCode, onDataLoad }: FreezeFramePanelProps) {
  const [freezeFrameData, setFreezeFrameData] = useState<FreezeFrameData[]>([]);
  const [selectedFrame, setSelectedFrame] = useState<FreezeFrameData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (dtcCode) {
      loadFreezeFrameData(dtcCode);
    }
  }, [dtcCode]);

  const loadFreezeFrameData = async (code?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const url = code 
        ? `/api/v1/frontend/freeze-frame/${code}`
        : '/api/v1/frontend/freeze-frame/all';
      
      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        setFreezeFrameData(result.data);
        if (result.data.length > 0) {
          setSelectedFrame(result.data[0]);
        }
        onDataLoad?.(result.data);
      } else {
        setError(result.error || 'Failed to load freeze frame data');
      }
    } catch (err) {
      setError('Network error loading freeze frame data');
    } finally {
      setIsLoading(false);
    }
  };

  const exportFreezeFrame = async () => {
    if (!selectedFrame) return;

    try {
      const response = await fetch('/api/v1/frontend/freeze-frame/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dtc_code: selectedFrame.dtc_code })
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `freeze_frame_${selectedFrame.dtc_code}_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  const formatValue = (value: number, unit: string) => {
    return `${value.toFixed(2)} ${unit}`;
  };

  const getParameterIcon = (param: string) => {
    switch (param) {
      case 'engine_speed': return <Gauge className="w-4 h-4" />;
      case 'vehicle_speed': return <Wind className="w-4 h-4" />;
      case 'engine_coolant_temp':
      case 'intake_air_temp': return <Thermometer className="w-4 h-4" />;
      case 'fuel_trim_bank1':
      case 'fuel_trim_bank2': return <Fuel className="w-4 h-4" />;
      default: return <Gauge className="w-4 h-4" />;
    }
  };

  const renderParameterGrid = () => {
    if (!selectedFrame) return null;

    const parameters = [
      { key: 'engine_speed', label: 'Engine Speed', value: selectedFrame.engine_speed, unit: 'RPM' },
      { key: 'vehicle_speed', label: 'Vehicle Speed', value: selectedFrame.vehicle_speed, unit: 'km/h' },
      { key: 'engine_coolant_temp', label: 'Coolant Temp', value: selectedFrame.engine_coolant_temp, unit: '°C' },
      { key: 'intake_air_temp', label: 'Intake Air Temp', value: selectedFrame.intake_air_temp, unit: '°C' },
      { key: 'manifold_pressure', label: 'Manifold Pressure', value: selectedFrame.manifold_pressure, unit: 'kPa' },
      { key: 'throttle_position', label: 'Throttle Position', value: selectedFrame.throttle_position, unit: '%' },
      { key: 'fuel_trim_bank1', label: 'Fuel Trim Bank 1', value: selectedFrame.fuel_trim_bank1, unit: '%' },
      { key: 'fuel_trim_bank2', label: 'Fuel Trim Bank 2', value: selectedFrame.fuel_trim_bank2, unit: '%' },
      { key: 'oxygen_sensor_voltage', label: 'O2 Sensor Voltage', value: selectedFrame.oxygen_sensor_voltage, unit: 'V' },
      { key: 'engine_load', label: 'Engine Load', value: selectedFrame.engine_load, unit: '%' },
      { key: 'fuel_pressure', label: 'Fuel Pressure', value: selectedFrame.fuel_pressure, unit: 'kPa' },
      { key: 'ignition_timing', label: 'Ignition Timing', value: selectedFrame.ignition_timing, unit: '°' },
      { key: 'air_flow_rate', label: 'Air Flow Rate', value: selectedFrame.air_flow_rate, unit: 'g/s' }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {parameters.map((param) => (
          <div key={param.key} className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              {getParameterIcon(param.key)}
              <span className="text-sm font-medium text-gray-700">{param.label}</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatValue(param.value, param.unit)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Snowflake className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Freeze Frame Data</h2>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => loadFreezeFrameData()}
            disabled={isLoading}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
            <span>Refresh</span>
          </button>
          
          {selectedFrame && (
            <button
              onClick={exportFreezeFrame}
              className="btn btn-primary flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading freeze frame data...</span>
        </div>
      )}

      {/* Freeze Frame Selection */}
      {freezeFrameData.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Freeze Frames</h3>
          <div className="space-y-2">
            {freezeFrameData.map((frame, index) => (
              <button
                key={index}
                onClick={() => setSelectedFrame(frame)}
                className={cn(
                  'w-full text-left p-3 rounded-lg border transition-colors',
                  selectedFrame === frame
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                )}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">{frame.dtc_code}</div>
                    <div className="text-sm text-gray-600">
                      {new Date(frame.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <Eye className="w-4 h-4 text-gray-400" />
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Selected Freeze Frame Details */}
      {selectedFrame && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Freeze Frame: {selectedFrame.dtc_code}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>{new Date(selectedFrame.timestamp).toLocaleString()}</span>
            </div>
          </div>
          
          {renderParameterGrid()}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && freezeFrameData.length === 0 && !error && (
        <div className="text-center py-8">
          <Snowflake className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Freeze Frame Data</h3>
          <p className="text-gray-600">
            No freeze frame data available for the current DTCs.
          </p>
        </div>
      )}
    </div>
  );
}
