import React, { useState } from 'react';
import { AlertTriangle, Shield, X, Check } from 'lucide-react';
import { cn } from '../lib/utils';
import { ConfirmationDialogConfig } from '../types/diagnostic';

interface ConfirmationDialogProps {
  isOpen: boolean;
  config: ConfirmationDialogConfig;
  onConfirm: () => void;
  onCancel: () => void;
}

export function ConfirmationDialog({ isOpen, config, onConfirm, onCancel }: ConfirmationDialogProps) {
  const [firstConfirm, setFirstConfirm] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const getSeverityIcon = () => {
    switch (config.severity) {
      case 'critical':
        return <AlertTriangle className="w-8 h-8 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-8 h-8 text-orange-600" />;
      case 'medium':
        return <Shield className="w-8 h-8 text-yellow-600" />;
      default:
        return <Shield className="w-8 h-8 text-blue-600" />;
    }
  };

  const getSeverityColors = () => {
    switch (config.severity) {
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'high':
        return 'border-orange-200 bg-orange-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const handleConfirm = async () => {
    if (config.requireDoubleConfirm && !firstConfirm) {
      setFirstConfirm(true);
      return;
    }

    setIsProcessing(true);
    try {
      await onConfirm();
    } finally {
      setIsProcessing(false);
      setFirstConfirm(false);
    }
  };

  const handleCancel = () => {
    setFirstConfirm(false);
    onCancel();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className={cn('p-6 border-l-4', getSeverityColors())}>
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              {getSeverityIcon()}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {config.title}
              </h3>
              <p className="text-gray-700 mb-4">
                {config.message}
              </p>
              
              {config.requireDoubleConfirm && firstConfirm && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                  <p className="text-yellow-800 text-sm font-medium">
                    ⚠️ Please confirm again to proceed with this critical operation
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            disabled={isProcessing}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
          >
            <X className="w-4 h-4 inline mr-2" />
            {config.cancelText || 'Cancel'}
          </button>
          <button
            onClick={handleConfirm}
            disabled={isProcessing}
            className={cn(
              'px-4 py-2 text-white rounded-lg flex items-center space-x-2 disabled:opacity-50',
              config.severity === 'critical' ? 'bg-red-600 hover:bg-red-700' :
              config.severity === 'high' ? 'bg-orange-600 hover:bg-orange-700' :
              config.severity === 'medium' ? 'bg-yellow-600 hover:bg-yellow-700' :
              'bg-blue-600 hover:bg-blue-700'
            )}
          >
            {isProcessing ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Check className="w-4 h-4" />
                <span>
                  {firstConfirm ? 'Confirm Again' : (config.confirmText || 'Confirm')}
                </span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
