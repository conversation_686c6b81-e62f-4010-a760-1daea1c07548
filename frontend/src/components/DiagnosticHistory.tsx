'use client';

import { useState, useEffect } from 'react';
import { 
  History, 
  Calendar, 
  Car, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Download,
  Trash2,
  Eye,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DiagnosticHistoryEntry {
  id: string;
  timestamp: string;
  vehicle_info: {
    make: string;
    model: string;
    year: number;
    vin?: string;
  };
  session_type: 'full_scan' | 'quick_scan' | 'live_data' | 'service_reset';
  dtc_count: number;
  dtc_codes: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  ai_analysis_performed: boolean;
  duration_seconds: number;
  connection_type: 'usb' | 'bluetooth' | 'mock';
  notes?: string;
  export_available: boolean;
}

interface DiagnosticHistoryProps {
  onSessionSelect?: (sessionId: string) => void;
  onExportSession?: (sessionId: string) => void;
}

export function DiagnosticHistory({ 
  onSessionSelect, 
  onExportSession 
}: DiagnosticHistoryProps) {
  const [historyEntries, setHistoryEntries] = useState<DiagnosticHistoryEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<DiagnosticHistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedSessionType, setSelectedSessionType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('all');

  useEffect(() => {
    loadHistory();
  }, []);

  useEffect(() => {
    filterEntries();
  }, [historyEntries, searchTerm, selectedSeverity, selectedSessionType, dateRange]);

  const loadHistory = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/frontend/history/sessions');
      const result = await response.json();

      if (result.success) {
        setHistoryEntries(result.data);
      } else {
        setError(result.error || 'Failed to load diagnostic history');
      }
    } catch (err) {
      setError('Network error loading history');
    } finally {
      setIsLoading(false);
    }
  };

  const filterEntries = () => {
    let filtered = [...historyEntries];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(entry => 
        entry.vehicle_info.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.vehicle_info.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.dtc_codes.some(code => code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        entry.vehicle_info.vin?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Severity filter
    if (selectedSeverity !== 'all') {
      filtered = filtered.filter(entry => entry.severity === selectedSeverity);
    }

    // Session type filter
    if (selectedSessionType !== 'all') {
      filtered = filtered.filter(entry => entry.session_type === selectedSessionType);
    }

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      if (dateRange !== 'all') {
        filtered = filtered.filter(entry => 
          new Date(entry.timestamp) >= filterDate
        );
      }
    }

    setFilteredEntries(filtered);
  };

  const deleteSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/v1/frontend/history/sessions/${sessionId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setHistoryEntries(prev => prev.filter(entry => entry.id !== sessionId));
      }
    } catch (err) {
      console.error('Failed to delete session:', err);
    }
  };

  const exportSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/v1/frontend/history/sessions/${sessionId}/export`);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `diagnostic_session_${sessionId}_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      onExportSession?.(sessionId);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case 'full_scan': return <AlertTriangle className="w-4 h-4" />;
      case 'quick_scan': return <CheckCircle className="w-4 h-4" />;
      case 'live_data': return <Clock className="w-4 h-4" />;
      case 'service_reset': return <Car className="w-4 h-4" />;
      default: return <History className="w-4 h-4" />;
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <History className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Diagnostic History</h2>
        </div>
        
        <button
          onClick={loadHistory}
          disabled={isLoading}
          className="btn btn-secondary flex items-center space-x-2"
        >
          <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by make, model, VIN, or DTC..."
                className="input pl-10"
              />
            </div>
          </div>

          {/* Severity Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="input"
            >
              <option value="all">All Severities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Session Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Session Type</label>
            <select
              value={selectedSessionType}
              onChange={(e) => setSelectedSessionType(e.target.value)}
              className="input"
            >
              <option value="all">All Types</option>
              <option value="full_scan">Full Scan</option>
              <option value="quick_scan">Quick Scan</option>
              <option value="live_data">Live Data</option>
              <option value="service_reset">Service Reset</option>
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="input"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="year">Last Year</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading diagnostic history...</span>
        </div>
      )}

      {/* History Entries */}
      {filteredEntries.length > 0 && (
        <div className="space-y-4">
          {filteredEntries.map((entry) => (
            <div key={entry.id} className="card hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex items-center space-x-2">
                      {getSessionTypeIcon(entry.session_type)}
                      <span className="font-medium text-gray-900 capitalize">
                        {entry.session_type.replace('_', ' ')}
                      </span>
                    </div>
                    <span className={cn(
                      'px-2 py-1 text-xs rounded-full capitalize',
                      getSeverityColor(entry.severity)
                    )}>
                      {entry.severity}
                    </span>
                    {entry.ai_analysis_performed && (
                      <span className="px-2 py-1 text-xs bg-purple-100 text-purple-600 rounded-full">
                        AI Analyzed
                      </span>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                    <div>
                      <span className="text-sm text-gray-600">Vehicle:</span>
                      <div className="font-medium">
                        {entry.vehicle_info.year} {entry.vehicle_info.make} {entry.vehicle_info.model}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Date:</span>
                      <div className="font-medium">
                        {new Date(entry.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">DTCs Found:</span>
                      <div className="font-medium">{entry.dtc_count}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Duration:</span>
                      <div className="font-medium">{formatDuration(entry.duration_seconds)}</div>
                    </div>
                  </div>

                  {entry.dtc_codes.length > 0 && (
                    <div className="mb-3">
                      <span className="text-sm text-gray-600">DTC Codes:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {entry.dtc_codes.slice(0, 5).map((code) => (
                          <span key={code} className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                            {code}
                          </span>
                        ))}
                        {entry.dtc_codes.length > 5 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            +{entry.dtc_codes.length - 5} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {entry.notes && (
                    <div className="text-sm text-gray-600 italic">
                      "{entry.notes}"
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => onSessionSelect?.(entry.id)}
                    className="btn btn-secondary btn-sm flex items-center space-x-1"
                  >
                    <Eye className="w-3 h-3" />
                    <span>View</span>
                  </button>
                  
                  {entry.export_available && (
                    <button
                      onClick={() => exportSession(entry.id)}
                      className="btn btn-primary btn-sm flex items-center space-x-1"
                    >
                      <Download className="w-3 h-3" />
                      <span>Export</span>
                    </button>
                  )}
                  
                  <button
                    onClick={() => deleteSession(entry.id)}
                    className="btn bg-red-600 text-white hover:bg-red-700 btn-sm flex items-center space-x-1"
                  >
                    <Trash2 className="w-3 h-3" />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredEntries.length === 0 && !error && (
        <div className="text-center py-8">
          <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {historyEntries.length === 0 ? 'No Diagnostic History' : 'No Results Found'}
          </h3>
          <p className="text-gray-600">
            {historyEntries.length === 0 
              ? 'Start your first diagnostic session to see history here.'
              : 'Try adjusting your filters to see more results.'
            }
          </p>
        </div>
      )}
    </div>
  );
}
