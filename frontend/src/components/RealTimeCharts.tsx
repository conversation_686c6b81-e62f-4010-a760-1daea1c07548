'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Activity, 
  Gauge, 
  Thermometer,
  Fuel,
  Wind,
  Zap,
  Play,
  Pause,
  RotateCcw,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SensorData {
  timestamp: number;
  engine_speed: number;
  vehicle_speed: number;
  engine_coolant_temp: number;
  intake_air_temp: number;
  throttle_position: number;
  engine_load: number;
  fuel_trim_bank1: number;
  fuel_trim_bank2: number;
  manifold_pressure: number;
  oxygen_sensor_voltage: number;
}

interface ChartConfig {
  id: string;
  name: string;
  dataKey: string;
  color: string;
  unit: string;
  min?: number;
  max?: number;
  icon: React.ReactNode;
}

interface RealTimeChartsProps {
  isMonitoring: boolean;
  onMonitoringToggle: (monitoring: boolean) => void;
  updateInterval?: number;
  onIntervalChange?: (interval: number) => void;
}

export function RealTimeCharts({ 
  isMonitoring, 
  onMonitoringToggle,
  updateInterval = 1000,
  onIntervalChange
}: RealTimeChartsProps) {
  const [sensorData, setSensorData] = useState<SensorData[]>([]);
  const [selectedCharts, setSelectedCharts] = useState<string[]>(['engine_speed', 'vehicle_speed', 'engine_coolant_temp', 'throttle_position']);
  const [maxDataPoints, setMaxDataPoints] = useState(50);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const chartConfigs: ChartConfig[] = [
    {
      id: 'engine_speed',
      name: 'Engine Speed',
      dataKey: 'engine_speed',
      color: '#3B82F6',
      unit: 'RPM',
      min: 0,
      max: 8000,
      icon: <Gauge className="w-4 h-4" />
    },
    {
      id: 'vehicle_speed',
      name: 'Vehicle Speed',
      dataKey: 'vehicle_speed',
      color: '#10B981',
      unit: 'km/h',
      min: 0,
      max: 200,
      icon: <Wind className="w-4 h-4" />
    },
    {
      id: 'engine_coolant_temp',
      name: 'Coolant Temperature',
      dataKey: 'engine_coolant_temp',
      color: '#EF4444',
      unit: '°C',
      min: 0,
      max: 120,
      icon: <Thermometer className="w-4 h-4" />
    },
    {
      id: 'intake_air_temp',
      name: 'Intake Air Temperature',
      dataKey: 'intake_air_temp',
      color: '#F59E0B',
      unit: '°C',
      min: -20,
      max: 80,
      icon: <Thermometer className="w-4 h-4" />
    },
    {
      id: 'throttle_position',
      name: 'Throttle Position',
      dataKey: 'throttle_position',
      color: '#8B5CF6',
      unit: '%',
      min: 0,
      max: 100,
      icon: <Zap className="w-4 h-4" />
    },
    {
      id: 'engine_load',
      name: 'Engine Load',
      dataKey: 'engine_load',
      color: '#06B6D4',
      unit: '%',
      min: 0,
      max: 100,
      icon: <Activity className="w-4 h-4" />
    },
    {
      id: 'fuel_trim_bank1',
      name: 'Fuel Trim Bank 1',
      dataKey: 'fuel_trim_bank1',
      color: '#84CC16',
      unit: '%',
      min: -25,
      max: 25,
      icon: <Fuel className="w-4 h-4" />
    },
    {
      id: 'fuel_trim_bank2',
      name: 'Fuel Trim Bank 2',
      dataKey: 'fuel_trim_bank2',
      color: '#F97316',
      unit: '%',
      min: -25,
      max: 25,
      icon: <Fuel className="w-4 h-4" />
    },
    {
      id: 'manifold_pressure',
      name: 'Manifold Pressure',
      dataKey: 'manifold_pressure',
      color: '#EC4899',
      unit: 'kPa',
      min: 0,
      max: 250,
      icon: <Gauge className="w-4 h-4" />
    },
    {
      id: 'oxygen_sensor_voltage',
      name: 'O2 Sensor Voltage',
      dataKey: 'oxygen_sensor_voltage',
      color: '#6366F1',
      unit: 'V',
      min: 0,
      max: 1,
      icon: <Zap className="w-4 h-4" />
    }
  ];

  useEffect(() => {
    if (isMonitoring) {
      startDataCollection();
    } else {
      stopDataCollection();
    }

    return () => {
      stopDataCollection();
    };
  }, [isMonitoring, updateInterval]);

  const startDataCollection = () => {
    intervalRef.current = setInterval(fetchSensorData, updateInterval);
  };

  const stopDataCollection = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const fetchSensorData = async () => {
    try {
      const response = await fetch('/api/v1/enhanced/live-data/current');
      const result = await response.json();

      if (result.success && result.data.sensors) {
        const newDataPoint: SensorData = {
          timestamp: Date.now(),
          ...result.data.sensors
        };

        setSensorData(prev => {
          const updated = [...prev, newDataPoint];
          return updated.slice(-maxDataPoints);
        });
        setError(null);
      }
    } catch (err) {
      setError('Failed to fetch sensor data');
    }
  };

  const clearData = () => {
    setSensorData([]);
  };

  const toggleChart = (chartId: string) => {
    setSelectedCharts(prev => 
      prev.includes(chartId) 
        ? prev.filter(id => id !== chartId)
        : [...prev, chartId]
    );
  };

  const renderMiniChart = (config: ChartConfig) => {
    const data = sensorData.slice(-20); // Last 20 points for mini chart
    if (data.length === 0) return null;

    const values = data.map(d => d[config.dataKey as keyof SensorData] as number);
    const minValue = config.min ?? Math.min(...values);
    const maxValue = config.max ?? Math.max(...values);
    const range = maxValue - minValue;

    const points = values.map((value, index) => {
      const x = (index / (values.length - 1)) * 100;
      const y = 100 - ((value - minValue) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    const currentValue = values[values.length - 1];
    const previousValue = values[values.length - 2];
    const trend = currentValue > previousValue ? 'up' : currentValue < previousValue ? 'down' : 'stable';

    return (
      <div className="bg-white rounded-lg border p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div style={{ color: config.color }}>
              {config.icon}
            </div>
            <span className="text-sm font-medium text-gray-900">{config.name}</span>
          </div>
          <div className="flex items-center space-x-1">
            {trend === 'up' && <TrendingUp className="w-3 h-3 text-green-500" />}
            {trend === 'down' && <TrendingUp className="w-3 h-3 text-red-500 rotate-180" />}
            {trend === 'stable' && <div className="w-3 h-3 bg-gray-400 rounded-full" />}
          </div>
        </div>
        
        <div className="mb-2">
          <span className="text-2xl font-bold text-gray-900">
            {currentValue?.toFixed(1) || '--'}
          </span>
          <span className="text-sm text-gray-600 ml-1">{config.unit}</span>
        </div>

        <div className="h-16 relative">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <polyline
              fill="none"
              stroke={config.color}
              strokeWidth="2"
              points={points}
            />
          </svg>
        </div>

        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{minValue}</span>
          <span>{maxValue}</span>
        </div>
      </div>
    );
  };

  const renderFullChart = (config: ChartConfig) => {
    if (sensorData.length === 0) return null;

    const values = sensorData.map(d => d[config.dataKey as keyof SensorData] as number);
    const minValue = config.min ?? Math.min(...values);
    const maxValue = config.max ?? Math.max(...values);
    const range = maxValue - minValue;

    const points = sensorData.map((data, index) => {
      const x = (index / (sensorData.length - 1)) * 100;
      const y = 100 - ((values[index] - minValue) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <div style={{ color: config.color }}>
              {config.icon}
            </div>
            <span>{config.name}</span>
          </h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {values[values.length - 1]?.toFixed(1) || '--'}
            </div>
            <div className="text-sm text-gray-600">{config.unit}</div>
          </div>
        </div>

        <div className="h-48 relative">
          <svg className="w-full h-full border rounded" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* Grid lines */}
            <defs>
              <pattern id={`grid-${config.id}`} width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f3f4f6" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill={`url(#grid-${config.id})`} />
            
            {/* Data line */}
            <polyline
              fill="none"
              stroke={config.color}
              strokeWidth="2"
              points={points}
            />
            
            {/* Current value dot */}
            {sensorData.length > 0 && (
              <circle
                cx={((sensorData.length - 1) / (sensorData.length - 1)) * 100}
                cy={100 - ((values[values.length - 1] - minValue) / range) * 100}
                r="2"
                fill={config.color}
              />
            )}
          </svg>
          
          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 -ml-8">
            <span>{maxValue}</span>
            <span>{((maxValue + minValue) / 2).toFixed(0)}</span>
            <span>{minValue}</span>
          </div>
        </div>

        <div className="mt-2 text-xs text-gray-500 text-center">
          Time (last {sensorData.length} readings)
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Real-Time Charts</h2>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onMonitoringToggle(!isMonitoring)}
            className={cn(
              'btn flex items-center space-x-2',
              isMonitoring ? 'bg-red-600 text-white hover:bg-red-700' : 'btn-primary'
            )}
          >
            {isMonitoring ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            <span>{isMonitoring ? 'Stop' : 'Start'}</span>
          </button>
          
          <button
            onClick={clearData}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Clear</span>
          </button>
        </div>
      </div>

      {/* Settings */}
      <div className="card">
        <div className="flex items-center space-x-4 mb-4">
          <Settings className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Chart Settings</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Update Interval</label>
            <select
              value={updateInterval}
              onChange={(e) => onIntervalChange?.(Number(e.target.value))}
              className="input"
            >
              <option value={500}>0.5 seconds</option>
              <option value={1000}>1 second</option>
              <option value={2000}>2 seconds</option>
              <option value={5000}>5 seconds</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Max Data Points</label>
            <select
              value={maxDataPoints}
              onChange={(e) => setMaxDataPoints(Number(e.target.value))}
              className="input"
            >
              <option value={25}>25 points</option>
              <option value={50}>50 points</option>
              <option value={100}>100 points</option>
              <option value={200}>200 points</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Data Points</label>
            <div className="text-lg font-semibold text-gray-900 py-2">
              {sensorData.length} / {maxDataPoints}
            </div>
          </div>
        </div>
      </div>

      {/* Chart Selection */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Charts to Display</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {chartConfigs.map((config) => (
            <button
              key={config.id}
              onClick={() => toggleChart(config.id)}
              className={cn(
                'p-3 rounded-lg border text-left transition-colors',
                selectedCharts.includes(config.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              )}
            >
              <div className="flex items-center space-x-2 mb-1">
                <div style={{ color: config.color }}>
                  {config.icon}
                </div>
                <span className="text-sm font-medium">{config.name}</span>
              </div>
              <div className="text-xs text-gray-600">{config.unit}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Mini Charts Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {chartConfigs
          .filter(config => selectedCharts.includes(config.id))
          .map(config => (
            <div key={`mini-${config.id}`}>
              {renderMiniChart(config)}
            </div>
          ))}
      </div>

      {/* Full Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartConfigs
          .filter(config => selectedCharts.includes(config.id))
          .slice(0, 4) // Show max 4 full charts
          .map(config => (
            <div key={`full-${config.id}`}>
              {renderFullChart(config)}
            </div>
          ))}
      </div>

      {/* Empty State */}
      {!isMonitoring && sensorData.length === 0 && (
        <div className="text-center py-8">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
          <p className="text-gray-600">
            Start monitoring to see real-time sensor data charts.
          </p>
        </div>
      )}
    </div>
  );
}
