'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  HardDrive, 
  Wifi, 
  RefreshCw, 
  Download, 
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  Zap,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ECUInfo {
  ecu_id: string;
  name: string;
  part_number: string;
  software_version: string;
  hardware_version: string;
  calibration_id: string;
  serial_number: string;
  supplier: string;
  status: 'online' | 'offline' | 'error';
  supported_protocols: string[];
  last_communication: string;
  diagnostic_capabilities: string[];
  memory_info?: {
    total_size: number;
    used_size: number;
    free_size: number;
  };
}

interface ECUParameter {
  pid: string;
  name: string;
  value: any;
  unit: string;
  min_value?: number;
  max_value?: number;
  status: 'normal' | 'warning' | 'error';
}

interface ECUInfoPanelProps {
  onECUSelect?: (ecuId: string) => void;
  isExpertMode?: boolean;
}

export function ECUInfoPanel({ onECUSelect, isExpertMode = false }: ECUInfoPanelProps) {
  const [ecuList, setECUList] = useState<ECUInfo[]>([]);
  const [selectedECU, setSelectedECU] = useState<ECUInfo | null>(null);
  const [ecuParameters, setECUParameters] = useState<ECUParameter[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingParams, setIsLoadingParams] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadECUList();
  }, []);

  const loadECUList = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/frontend/ecu/list');
      const result = await response.json();

      if (result.success) {
        setECUList(result.data);
        if (result.data.length > 0) {
          selectECU(result.data[0]);
        }
      } else {
        setError(result.error || 'Failed to load ECU information');
      }
    } catch (err) {
      setError('Network error loading ECU data');
    } finally {
      setIsLoading(false);
    }
  };

  const selectECU = async (ecu: ECUInfo) => {
    setSelectedECU(ecu);
    onECUSelect?.(ecu.ecu_id);
    await loadECUParameters(ecu.ecu_id);
  };

  const loadECUParameters = async (ecuId: string) => {
    setIsLoadingParams(true);

    try {
      const response = await fetch(`/api/v1/frontend/ecu/${ecuId}/parameters`);
      const result = await response.json();

      if (result.success) {
        setECUParameters(result.data);
      }
    } catch (err) {
      console.error('Failed to load ECU parameters:', err);
    } finally {
      setIsLoadingParams(false);
    }
  };

  const exportECUData = async () => {
    if (!selectedECU) return;

    try {
      const response = await fetch(`/api/v1/frontend/ecu/${selectedECU.ecu_id}/export`, {
        method: 'POST'
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ecu_data_${selectedECU.ecu_id}_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'offline':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Cpu className="w-4 h-4 text-gray-400" />;
    }
  };

  const getParameterStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatMemorySize = (bytes: number) => {
    const kb = bytes / 1024;
    const mb = kb / 1024;
    
    if (mb >= 1) {
      return `${mb.toFixed(2)} MB`;
    } else if (kb >= 1) {
      return `${kb.toFixed(2)} KB`;
    } else {
      return `${bytes} B`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Cpu className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">ECU Information</h2>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={loadECUList}
            disabled={isLoading}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
            <span>Refresh</span>
          </button>
          
          {selectedECU && isExpertMode && (
            <button
              onClick={exportECUData}
              className="btn btn-primary flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading ECU information...</span>
        </div>
      )}

      {/* ECU List */}
      {ecuList.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Connected ECUs</h3>
          <div className="space-y-2">
            {ecuList.map((ecu) => (
              <button
                key={ecu.ecu_id}
                onClick={() => selectECU(ecu)}
                className={cn(
                  'w-full text-left p-4 rounded-lg border transition-colors',
                  selectedECU?.ecu_id === ecu.ecu_id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Cpu className="w-5 h-5 text-gray-600" />
                    <div>
                      <div className="font-medium text-gray-900">{ecu.name}</div>
                      <div className="text-sm text-gray-600">ID: {ecu.ecu_id}</div>
                      <div className="text-sm text-gray-600">Part: {ecu.part_number}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(ecu.status)}
                    <span className={cn(
                      'text-sm capitalize',
                      ecu.status === 'online' ? 'text-green-600' :
                      ecu.status === 'offline' ? 'text-gray-600' :
                      'text-red-600'
                    )}>
                      {ecu.status}
                    </span>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Selected ECU Details */}
      {selectedECU && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* ECU Information */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">ECU Details</h3>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <div className="text-gray-900">{selectedECU.name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedECU.status)}
                    <span className="capitalize">{selectedECU.status}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Part Number</label>
                  <div className="text-gray-900">{selectedECU.part_number}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Serial Number</label>
                  <div className="text-gray-900">{selectedECU.serial_number}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Software Version</label>
                  <div className="text-gray-900">{selectedECU.software_version}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Hardware Version</label>
                  <div className="text-gray-900">{selectedECU.hardware_version}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Calibration ID</label>
                  <div className="text-gray-900">{selectedECU.calibration_id}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Supplier</label>
                  <div className="text-gray-900">{selectedECU.supplier}</div>
                </div>
              </div>

              {/* Memory Information */}
              {selectedECU.memory_info && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                    <HardDrive className="w-4 h-4" />
                    <span>Memory Information</span>
                  </h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Total:</span>
                      <div className="font-medium">{formatMemorySize(selectedECU.memory_info.total_size)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Used:</span>
                      <div className="font-medium">{formatMemorySize(selectedECU.memory_info.used_size)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Free:</span>
                      <div className="font-medium">{formatMemorySize(selectedECU.memory_info.free_size)}</div>
                    </div>
                  </div>
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${(selectedECU.memory_info.used_size / selectedECU.memory_info.total_size) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Protocols */}
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                  <Wifi className="w-4 h-4" />
                  <span>Supported Protocols</span>
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedECU.supported_protocols.map((protocol) => (
                    <span 
                      key={protocol}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {protocol}
                    </span>
                  ))}
                </div>
              </div>

              {/* Capabilities */}
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>Diagnostic Capabilities</span>
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedECU.diagnostic_capabilities.map((capability) => (
                    <span 
                      key={capability}
                      className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                    >
                      {capability}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* ECU Parameters */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Live Parameters</h3>
              {isLoadingParams && (
                <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
              )}
            </div>
            
            {ecuParameters.length > 0 ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {ecuParameters.map((param) => (
                  <div key={param.pid} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{param.name}</div>
                        <div className="text-sm text-gray-600">PID: {param.pid}</div>
                      </div>
                      <div className="text-right">
                        <div className={cn('font-semibold', getParameterStatusColor(param.status))}>
                          {param.value} {param.unit}
                        </div>
                        {param.min_value !== undefined && param.max_value !== undefined && (
                          <div className="text-xs text-gray-500">
                            Range: {param.min_value} - {param.max_value} {param.unit}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Zap className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No live parameters available</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && ecuList.length === 0 && !error && (
        <div className="text-center py-8">
          <Cpu className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No ECUs Found</h3>
          <p className="text-gray-600">
            No ECUs detected. Please ensure the vehicle is connected and ignition is on.
          </p>
        </div>
      )}
    </div>
  );
}
