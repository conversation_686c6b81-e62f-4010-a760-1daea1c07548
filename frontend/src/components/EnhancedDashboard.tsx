'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Shield, 
  ShieldAlert, 
  Car, 
  Activity, 
  AlertTriangle, 
  CheckCircle,
  Settings,
  Unlock,
  Lock,
  Eye,
  EyeOff,
  Gauge,
  Thermometer,
  Fuel,
  Wind,
  Zap,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  RotateCcw,
  Brain,
  Clock
} from 'lucide-react';
import { LiveDataDashboard } from './LiveDataDashboard';
import { ConnectionSelector } from './ConnectionSelector';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface VehicleInfo {
  make?: string;
  model?: string;
  year?: number;
  vin?: string;
  engine_type?: string;
  fuel_type?: string;
  confidence?: number;
  detection_method?: string;
}

interface SafetyModeStatus {
  is_safe_mode: boolean;
  expert_mode_available: boolean;
  restricted_operations: string[];
  warnings: string[];
}

interface ConnectionStatus {
  connected: boolean;
  port?: string;
  protocol?: string;
  vehicle_info?: VehicleInfo;
  error_message?: string;
  last_activity?: string;
}

export function EnhancedDashboard() {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo | null>(null);
  const [safetyMode, setSafetyMode] = useState<SafetyModeStatus>({
    is_safe_mode: true,
    expert_mode_available: false,
    restricted_operations: [],
    warnings: []
  });
  const [isExpertMode, setIsExpertMode] = useState(false);
  const [showExpertDialog, setShowExpertDialog] = useState(false);
  const [expertUnlockCode, setExpertUnlockCode] = useState('');
  const [autoDetecting, setAutoDetecting] = useState(false);

  useEffect(() => {
    checkExistingConnection();
    checkSafetyModeStatus();
  }, []);

  useEffect(() => {
    if (isConnected) {
      autoDetectVehicle();
    }
  }, [isConnected]);

  const checkExistingConnection = async () => {
    try {
      const response = await fetch('/api/v1/status');
      if (response.ok) {
        const result = await response.json();
        if (result.connected) {
          setIsConnected(true);
          setConnectionStatus(result);
          if (result.vehicle_info) {
            setVehicleInfo(result.vehicle_info);
          }
        }
      }
    } catch (error) {
      console.log('No existing connection found');
    }
  };

  const checkSafetyModeStatus = async () => {
    try {
      const response = await fetch('/api/v1/enhanced/safety-mode/status');
      if (response.ok) {
        const result = await response.json();
        setSafetyMode(result);
      }
    } catch (error) {
      console.error('Failed to check safety mode:', error);
    }
  };

  const autoDetectVehicle = async () => {
    if (autoDetecting) return;
    
    setAutoDetecting(true);
    try {
      const response = await fetch('/api/v1/vehicle/detect');
      if (response.ok) {
        const result = await response.json();
        if (result.make && result.confidence > 0.5) {
          const detectedInfo: VehicleInfo = {
            make: result.make,
            model: result.model,
            year: result.year,
            vin: result.vin,
            engine_type: result.engine_type,
            fuel_type: result.fuel_type,
            confidence: result.confidence,
            detection_method: result.detection_method
          };
          setVehicleInfo(detectedInfo);
          toast.success(`Vehicle detected: ${result.make} ${result.model || ''} ${result.year || ''}`);
        } else {
          toast.success('Vehicle detection completed but no specific vehicle identified');
        }
      }
    } catch (error) {
      console.error('Vehicle detection failed:', error);
      toast.error('Vehicle detection failed');
    } finally {
      setAutoDetecting(false);
    }
  };

  const handleConnectionSuccess = (connectionData: ConnectionStatus) => {
    setIsConnected(true);
    setConnectionStatus(connectionData);
    toast.success('Connected successfully');
  };

  const handleConnectionError = (error: string) => {
    setIsConnected(false);
    setConnectionStatus(null);
    toast.error(error);
  };

  const handleDisconnect = async () => {
    try {
      await fetch('/api/v1/disconnect', { method: 'POST' });
      setIsConnected(false);
      setConnectionStatus(null);
      setVehicleInfo(null);
      setIsExpertMode(false);
      toast.success('Disconnected successfully');
    } catch (error) {
      toast.error('Failed to disconnect');
    }
  };

  const requestExpertMode = async () => {
    if (!expertUnlockCode.trim()) {
      toast.error('Please enter unlock code');
      return;
    }

    try {
      const response = await fetch('/api/v1/enhanced/expert-mode/request', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_identifier: 'dashboard_user',
          unlock_code: expertUnlockCode,
          requested_permissions: ['WRITE_ECU', 'RESET_ADAPTATIONS']
        })
      });

      const result = await response.json();
      if (result.success) {
        setIsExpertMode(true);
        setShowExpertDialog(false);
        setExpertUnlockCode('');
        toast.success('Expert mode activated');
        
        // Show warnings
        if (result.warnings) {
          result.warnings.forEach((warning: string) => {
            toast.error(warning, { duration: 5000 });
          });
        }
      } else {
        toast.error(result.error || 'Failed to activate expert mode');
      }
    } catch (error) {
      toast.error('Failed to request expert mode');
    }
  };

  const exitExpertMode = async () => {
    try {
      await fetch('/api/v1/enhanced/expert-mode/exit', { method: 'POST' });
      setIsExpertMode(false);
      toast.success('Expert mode deactivated - Safe mode enabled');
    } catch (error) {
      toast.error('Failed to exit expert mode');
    }
  };

  const handleExpertModeToggle = () => {
    if (isExpertMode) {
      exitExpertMode();
    } else {
      setShowExpertDialog(true);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Car className="w-8 h-8 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                OBD2 AI Diagnostic System
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {/* Safety Mode Indicator */}
              <div className={cn(
                'flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium',
                isExpertMode 
                  ? 'bg-red-100 text-red-700 border border-red-200' 
                  : 'bg-green-100 text-green-700 border border-green-200'
              )}>
                {isExpertMode ? (
                  <>
                    <ShieldAlert className="w-4 h-4" />
                    <span>Expert Mode</span>
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4" />
                    <span>Safe Mode</span>
                  </>
                )}
              </div>

              {/* Connection Status */}
              {isConnected && (
                <div className="flex items-center space-x-2 text-sm text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span>Connected</span>
                </div>
              )}

              {/* Disconnect Button */}
              {isConnected && (
                <button
                  onClick={handleDisconnect}
                  className="px-3 py-1 text-sm text-red-600 hover:text-red-700"
                >
                  Disconnect
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!isConnected ? (
          <ConnectionSelector
            onConnectionSuccess={handleConnectionSuccess}
            onConnectionError={handleConnectionError}
          />
        ) : (
          <div className="space-y-6">
            {/* Vehicle Information */}
            {(vehicleInfo || autoDetecting) && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Car className="w-5 h-5 mr-2" />
                    Vehicle Information
                  </h2>
                  {autoDetecting && (
                    <div className="flex items-center space-x-2 text-sm text-blue-600">
                      <Activity className="w-4 h-4 animate-spin" />
                      <span>Auto-detecting...</span>
                    </div>
                  )}
                </div>

                {vehicleInfo && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Make</p>
                      <p className="font-medium">{vehicleInfo.make || 'Unknown'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Model</p>
                      <p className="font-medium">{vehicleInfo.model || 'Unknown'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Year</p>
                      <p className="font-medium">{vehicleInfo.year || 'Unknown'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Engine Type</p>
                      <p className="font-medium">{vehicleInfo.engine_type || 'Unknown'}</p>
                    </div>
                    {vehicleInfo.vin && (
                      <div className="md:col-span-2">
                        <p className="text-sm text-gray-500">VIN</p>
                        <p className="font-medium font-mono text-sm">{vehicleInfo.vin}</p>
                      </div>
                    )}
                    {vehicleInfo.confidence && (
                      <div>
                        <p className="text-sm text-gray-500">Detection Confidence</p>
                        <p className="font-medium">{(vehicleInfo.confidence * 100).toFixed(0)}%</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Live Data Dashboard */}
            <LiveDataDashboard
              isExpertMode={isExpertMode}
              onExpertModeToggle={handleExpertModeToggle}
            />
          </div>
        )}
      </div>

      {/* Expert Mode Dialog */}
      {showExpertDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-2 mb-4">
              <ShieldAlert className="w-6 h-6 text-red-600" />
              <h3 className="text-lg font-semibold text-gray-900">Expert Mode Access</h3>
            </div>
            
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Warning:</strong> Expert mode allows write operations that can modify vehicle settings. 
                Only proceed if you understand the risks.
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Unlock Code
              </label>
              <input
                type="password"
                value={expertUnlockCode}
                onChange={(e) => setExpertUnlockCode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter expert mode unlock code"
                onKeyPress={(e) => e.key === 'Enter' && requestExpertMode()}
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={requestExpertMode}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 font-medium"
              >
                <Unlock className="w-4 h-4 inline mr-2" />
                Activate Expert Mode
              </button>
              <button
                onClick={() => {
                  setShowExpertDialog(false);
                  setExpertUnlockCode('');
                }}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
