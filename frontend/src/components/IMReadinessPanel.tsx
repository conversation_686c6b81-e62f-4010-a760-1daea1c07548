import React, { useState, useEffect } from 'react';
import { Shield, CheckCircle, Clock, AlertTriangle, RefreshCw, Info, Lightbulb } from 'lucide-react';
import { cn } from '../lib/utils';
import { IMReadinessStatus, IMReadinessMonitor } from '../types/diagnostic';
import { apiClient } from '../lib/api';

interface IMReadinessPanelProps {
  onStatusUpdate?: (status: IMReadinessStatus) => void;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
}

export function IMReadinessPanel({ 
  onStatusUpdate, 
  autoRefresh = false, 
  refreshInterval = 30 
}: IMReadinessPanelProps) {
  const [readinessStatus, setReadinessStatus] = useState<IMReadinessStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const fetchReadinessStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.getIMReadiness();
      if (response.success && response.data) {
        setReadinessStatus(response.data);
        setLastUpdate(new Date());
        onStatusUpdate?.(response.data);
      } else {
        setError('Failed to retrieve I/M readiness status');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchReadinessStatus();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchReadinessStatus, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const getMonitorStatusIcon = (monitor: IMReadinessMonitor) => {
    switch (monitor.status) {
      case 'ready':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'not_ready':
        return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'not_applicable':
        return <Info className="w-5 h-5 text-gray-400" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default:
        return <Info className="w-5 h-5 text-gray-400" />;
    }
  };

  const getMonitorStatusColor = (monitor: IMReadinessMonitor) => {
    switch (monitor.status) {
      case 'ready':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'not_ready':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'not_applicable':
        return 'bg-gray-50 border-gray-200 text-gray-600';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-600';
    }
  };

  const getOverallStatusColor = (status?: string) => {
    switch (status) {
      case 'ready':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'not_ready':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'partial':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getReadinessPercentageColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!readinessStatus && !isLoading) {
    return (
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">I/M Readiness Status</h3>
          </div>
          <button
            onClick={fetchReadinessStatus}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Check Status</span>
          </button>
        </div>
        <div className="text-center py-8">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">I/M readiness status not available</p>
          <p className="text-sm text-gray-500 mt-2">
            Connect to OBD2 and click "Check Status" to view emission monitor readiness
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">I/M Readiness Status</h3>
        </div>
        <div className="flex items-center space-x-2">
          {lastUpdate && (
            <span className="text-xs text-gray-500">
              Updated: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={fetchReadinessStatus}
            disabled={isLoading}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
            <span>{isLoading ? 'Checking...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Checking I/M readiness status...</span>
        </div>
      ) : readinessStatus ? (
        <div className="space-y-6">
          {/* Overall Status Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={cn('p-4 rounded-lg border', getOverallStatusColor(readinessStatus.overall_status))}>
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="w-5 h-5" />
                <span className="font-semibold">Overall Status</span>
              </div>
              <p className="text-lg font-bold capitalize">{readinessStatus.overall_status}</p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-800">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-5 h-5" />
                <span className="font-semibold">Ready Monitors</span>
              </div>
              <p className="text-lg font-bold">
                {readinessStatus.ready_count} / {readinessStatus.total_count}
              </p>
            </div>

            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-gray-800">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-5 h-5 rounded-full bg-current opacity-20"></div>
                <span className="font-semibold">Readiness</span>
              </div>
              <p className={cn('text-lg font-bold', getReadinessPercentageColor(readinessStatus.readiness_percentage))}>
                {Math.round(readinessStatus.readiness_percentage)}%
              </p>
            </div>
          </div>

          {/* MIL Status */}
          <div className={cn(
            'p-4 rounded-lg border flex items-center space-x-3',
            readinessStatus.mil_status ? 'bg-red-50 border-red-200 text-red-800' : 'bg-green-50 border-green-200 text-green-800'
          )}>
            <Lightbulb className="w-6 h-6" />
            <div>
              <p className="font-semibold">
                Malfunction Indicator Lamp (MIL): {readinessStatus.mil_status ? 'ON' : 'OFF'}
              </p>
              <p className="text-sm opacity-90">
                {readinessStatus.mil_status 
                  ? 'Check engine light is active - diagnostic trouble codes present'
                  : 'No active malfunction detected'
                }
              </p>
            </div>
          </div>

          {/* Individual Monitor Status */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Individual Monitor Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {readinessStatus.monitors.map((monitor, index) => (
                <div
                  key={index}
                  className={cn('p-3 rounded-lg border', getMonitorStatusColor(monitor))}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getMonitorStatusIcon(monitor)}
                      <span className="font-medium">{monitor.name}</span>
                    </div>
                    <span className="text-xs font-medium uppercase">
                      {monitor.status.replace('_', ' ')}
                    </span>
                  </div>
                  <p className="text-sm mt-1 opacity-90">{monitor.description}</p>
                  {monitor.status === 'not_ready' && (
                    <p className="text-xs mt-2 opacity-75">
                      Drive cycle required to complete this monitor
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Readiness Tips */}
          {readinessStatus.overall_status !== 'ready' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-semibold text-blue-900 mb-2">Tips to Complete Readiness Monitors</h5>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Drive the vehicle for 15-20 minutes at highway speeds</li>
                    <li>• Allow the engine to reach normal operating temperature</li>
                    <li>• Perform city and highway driving cycles</li>
                    <li>• Ensure fuel tank is between 1/4 and 3/4 full</li>
                    <li>• Allow the vehicle to sit overnight before retesting</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      ) : null}
    </div>
  );
}
