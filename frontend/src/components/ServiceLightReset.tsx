'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle, 
  Clock, 
  RotateCcw,
  Shield,
  Car,
  Settings,
  Lightbulb,
  Timer
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ServiceResetOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  requiresExpert: boolean;
  estimatedTime: string;
  warning?: string;
}

interface ServiceLightResetProps {
  isExpertMode: boolean;
  vehicleInfo?: {
    make?: string;
    model?: string;
    year?: number;
  };
  onResetComplete?: (service: string, success: boolean) => void;
}

export function ServiceLightReset({ 
  isExpertMode, 
  vehicleInfo, 
  onResetComplete 
}: ServiceLightResetProps) {
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [isResetting, setIsResetting] = useState(false);
  const [resetResults, setResetResults] = useState<Record<string, boolean>>({});
  const [showConfirmation, setShowConfirmation] = useState(false);

  const serviceOptions: ServiceResetOption[] = [
    {
      id: 'oil_service',
      name: 'Oil Service Light',
      description: 'Reset oil change service reminder',
      icon: <Lightbulb className="w-5 h-5" />,
      requiresExpert: false,
      estimatedTime: '30 seconds',
      warning: 'Only reset after completing oil change service'
    },
    {
      id: 'maintenance',
      name: 'Maintenance Light',
      description: 'Reset general maintenance reminder',
      icon: <Wrench className="w-5 h-5" />,
      requiresExpert: false,
      estimatedTime: '30 seconds'
    },
    {
      id: 'inspection',
      name: 'Inspection Light',
      description: 'Reset inspection/MOT reminder',
      icon: <CheckCircle className="w-5 h-5" />,
      requiresExpert: false,
      estimatedTime: '45 seconds'
    },
    {
      id: 'airbag',
      name: 'Airbag Light',
      description: 'Reset airbag system warning',
      icon: <Shield className="w-5 h-5" />,
      requiresExpert: true,
      estimatedTime: '2 minutes',
      warning: 'Only reset after proper airbag system diagnosis and repair'
    },
    {
      id: 'abs',
      name: 'ABS Light',
      description: 'Reset ABS system warning',
      icon: <Car className="w-5 h-5" />,
      requiresExpert: true,
      estimatedTime: '1 minute',
      warning: 'Ensure ABS system is functioning properly before reset'
    },
    {
      id: 'engine',
      name: 'Engine Light (CEL)',
      description: 'Reset check engine light',
      icon: <Settings className="w-5 h-5" />,
      requiresExpert: true,
      estimatedTime: '1 minute',
      warning: 'Only reset after diagnosing and fixing underlying issues'
    },
    {
      id: 'service_interval',
      name: 'Service Interval',
      description: 'Reset service interval counter',
      icon: <Timer className="w-5 h-5" />,
      requiresExpert: false,
      estimatedTime: '1 minute'
    }
  ];

  const availableServices = serviceOptions.filter(service => 
    !service.requiresExpert || isExpertMode
  );

  const handleServiceSelect = (serviceId: string) => {
    setSelectedService(serviceId);
    setShowConfirmation(true);
  };

  const performReset = async () => {
    if (!selectedService) return;

    setIsResetting(true);
    setShowConfirmation(false);

    try {
      const response = await fetch('/api/v1/enhanced/service-reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          service_type: selectedService,
          vehicle_info: vehicleInfo,
          expert_mode: isExpertMode
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setResetResults(prev => ({ ...prev, [selectedService]: true }));
        onResetComplete?.(selectedService, true);
      } else {
        setResetResults(prev => ({ ...prev, [selectedService]: false }));
        onResetComplete?.(selectedService, false);
      }
    } catch (error) {
      setResetResults(prev => ({ ...prev, [selectedService]: false }));
      onResetComplete?.(selectedService, false);
    } finally {
      setIsResetting(false);
      setSelectedService(null);
    }
  };

  const getServiceStatus = (serviceId: string) => {
    if (isResetting && selectedService === serviceId) {
      return 'resetting';
    }
    if (resetResults[serviceId] === true) {
      return 'success';
    }
    if (resetResults[serviceId] === false) {
      return 'failed';
    }
    return 'idle';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resetting':
        return <RotateCcw className="w-4 h-4 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return null;
    }
  };

  const selectedServiceData = serviceOptions.find(s => s.id === selectedService);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Wrench className="w-6 h-6 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900">Service Light Reset</h2>
      </div>

      {/* Expert Mode Notice */}
      {!isExpertMode && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-yellow-600" />
            <span className="text-yellow-800">
              Some advanced resets require Expert Mode activation
            </span>
          </div>
        </div>
      )}

      {/* Service Options Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableServices.map((service) => {
          const status = getServiceStatus(service.id);
          const isDisabled = isResetting || (!isExpertMode && service.requiresExpert);
          
          return (
            <button
              key={service.id}
              onClick={() => !isDisabled && handleServiceSelect(service.id)}
              disabled={isDisabled}
              className={cn(
                'card p-4 text-left transition-all duration-200',
                isDisabled 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'hover:shadow-md cursor-pointer hover:border-blue-300',
                status === 'success' && 'border-green-500 bg-green-50',
                status === 'failed' && 'border-red-500 bg-red-50'
              )}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={cn(
                    'p-2 rounded-lg',
                    status === 'success' ? 'bg-green-100 text-green-600' :
                    status === 'failed' ? 'bg-red-100 text-red-600' :
                    'bg-blue-100 text-blue-600'
                  )}>
                    {service.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {service.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {service.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{service.estimatedTime}</span>
                      </div>
                      {service.requiresExpert && (
                        <div className="flex items-center space-x-1">
                          <Shield className="w-3 h-3" />
                          <span>Expert Mode</span>
                        </div>
                      )}
                    </div>
                    {service.warning && (
                      <div className="mt-2 text-xs text-orange-600 flex items-start space-x-1">
                        <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                        <span>{service.warning}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="ml-2">
                  {getStatusIcon(status)}
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Confirmation Dialog */}
      {showConfirmation && selectedServiceData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
              <h3 className="text-lg font-semibold text-gray-900">Confirm Reset</h3>
            </div>
            
            <p className="text-gray-600 mb-4">
              Are you sure you want to reset the <strong>{selectedServiceData.name}</strong>?
            </p>
            
            {selectedServiceData.warning && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                <p className="text-orange-800 text-sm">
                  <strong>Warning:</strong> {selectedServiceData.warning}
                </p>
              </div>
            )}
            
            <div className="flex items-center space-x-3">
              <button
                onClick={performReset}
                className="btn btn-primary flex-1"
              >
                Confirm Reset
              </button>
              <button
                onClick={() => {
                  setShowConfirmation(false);
                  setSelectedService(null);
                }}
                className="btn btn-secondary flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reset History */}
      {Object.keys(resetResults).length > 0 && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Resets</h3>
          <div className="space-y-2">
            {Object.entries(resetResults).map(([serviceId, success]) => {
              const service = serviceOptions.find(s => s.id === serviceId);
              return (
                <div key={serviceId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-900">{service?.name}</span>
                  <div className="flex items-center space-x-2">
                    {success ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-600 text-sm">Success</span>
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                        <span className="text-red-600 text-sm">Failed</span>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
