// API client for OBD2 Diagnostic System
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  APIResponse, 
  ConnectionStatus, 
  DiagnosticSession, 
  DiagnosticHistory,
  ConnectionForm,
  ScanOptions,
  MockScenario
} from '@/types/diagnostic';

class APIClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
          console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
        }
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
          console.log('API Response:', response.status, response.data);
        }
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // System health check
  async healthCheck(): Promise<APIResponse> {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Connection management
  async connect(connectionForm: ConnectionForm): Promise<APIResponse<ConnectionStatus>> {
    try {
      const response = await this.client.post('/api/obd/connect', connectionForm);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async disconnect(): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/obd/disconnect');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getConnectionStatus(): Promise<APIResponse<ConnectionStatus>> {
    try {
      const response = await this.client.get('/api/obd/status');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Available ports and devices
  async getAvailablePorts(): Promise<APIResponse<string[]>> {
    try {
      const response = await this.client.get('/api/obd/ports');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Diagnostic scanning
  async startDiagnosticScan(options: ScanOptions): Promise<APIResponse<{ session_id: string }>> {
    try {
      const response = await this.client.post('/api/diagnostic/scan', options);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getDiagnosticSession(sessionId: string): Promise<APIResponse<DiagnosticSession>> {
    try {
      const response = await this.client.get(`/api/diagnostic/session/${sessionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async stopDiagnosticScan(sessionId: string): Promise<APIResponse> {
    try {
      const response = await this.client.post(`/api/diagnostic/session/${sessionId}/stop`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // History and data management
  async getDiagnosticHistory(limit?: number): Promise<APIResponse<DiagnosticHistory>> {
    try {
      const params = limit ? { limit } : {};
      const response = await this.client.get('/api/diagnostic/history', { params });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deleteDiagnosticSession(sessionId: string): Promise<APIResponse> {
    try {
      const response = await this.client.delete(`/api/diagnostic/session/${sessionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Mock scenarios for testing
  async getMockScenarios(): Promise<APIResponse<MockScenario[]>> {
    try {
      const response = await this.client.get('/api/mock/scenarios');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // AI analysis
  async requestAIAnalysis(sessionId: string): Promise<APIResponse> {
    try {
      const response = await this.client.post(`/api/diagnostic/session/${sessionId}/analyze`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Enhanced DTC operations
  async getDTCsByMode(mode: 'stored' | 'pending' | 'permanent'): Promise<APIResponse> {
    try {
      const response = await this.client.get(`/api/v1/dtcs/${mode}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async clearDTCs(confirm: boolean = false, mode?: string): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/dtcs/clear', {
        confirm,
        mode: mode || 'all'
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async clearSingleDTC(code: string, confirm: boolean = false): Promise<APIResponse> {
    try {
      const response = await this.client.post(`/api/v1/dtcs/${code}/clear`, { confirm });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Vehicle information
  async getVehicleInfo(): Promise<APIResponse> {
    try {
      const response = await this.client.get('/api/v1/vehicle/info');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getOdometer(): Promise<APIResponse> {
    try {
      const response = await this.client.get('/api/v1/vehicle/odometer');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // I/M Readiness monitoring
  async getIMReadiness(): Promise<APIResponse> {
    try {
      const response = await this.client.get('/api/v1/im-readiness');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Live data monitoring
  async startLiveMonitoring(sensors?: string[], updateInterval?: number): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/live-data/start', {
        sensors,
        update_interval: updateInterval || 1.0
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getCurrentLiveData(): Promise<APIResponse> {
    try {
      const response = await this.client.get('/api/v1/enhanced/live-data/current');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async stopLiveMonitoring(): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/live-data/stop');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Expert mode operations
  async activateExpertMode(unlockCode: string): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/expert-mode/activate', {
        unlock_code: unlockCode,
        user_identifier: 'web_client'
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deactivateExpertMode(): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/expert-mode/deactivate');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Service operations (Expert mode required)
  async resetServiceLight(vehicleType: string): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/service/reset-light', {
        vehicle_type: vehicleType
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async resetOilLife(): Promise<APIResponse> {
    try {
      const response = await this.client.post('/api/v1/enhanced/service/reset-oil-life');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handling
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.error || error.response.data?.message || 'Server error';
      return new Error(`API Error: ${message} (${error.response.status})`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error: Unable to connect to server');
    } else {
      // Something else happened
      return new Error(`Request error: ${error.message}`);
    }
  }

  // WebSocket connection for real-time updates
  createWebSocket(sessionId?: string): WebSocket | null {
    try {
      const wsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8000/ws';
      const url = sessionId ? `${wsUrl}/${sessionId}` : wsUrl;
      return new WebSocket(url);
    } catch (error) {
      console.error('WebSocket connection error:', error);
      return null;
    }
  }
}

// Create singleton instance
export const apiClient = new APIClient();

// Export individual methods for convenience
export const {
  healthCheck,
  connect,
  disconnect,
  getConnectionStatus,
  getAvailablePorts,
  startDiagnosticScan,
  getDiagnosticSession,
  stopDiagnosticScan,
  getDiagnosticHistory,
  deleteDiagnosticSession,
  getMockScenarios,
  requestAIAnalysis,
  createWebSocket,
  // Enhanced DTC operations
  getDTCsByMode,
  clearDTCs,
  clearSingleDTC,
  // Vehicle information
  getVehicleInfo,
  getOdometer,
  // I/M Readiness
  getIMReadiness,
  // Live data monitoring
  startLiveMonitoring,
  getCurrentLiveData,
  stopLiveMonitoring,
  // Expert mode
  activateExpertMode,
  deactivateExpertMode,
  // Service operations
  resetServiceLight,
  resetOilLife
} = apiClient;
