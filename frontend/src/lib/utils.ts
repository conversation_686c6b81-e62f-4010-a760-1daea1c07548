import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
}

export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

export function getSeverityColor(severity: string): string {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'high':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
      return 'text-green-600 bg-green-50 border-green-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'text-green-600 bg-green-50';
    case 'connecting':
    case 'scanning':
    case 'analyzing':
      return 'text-blue-600 bg-blue-50';
    case 'error':
      return 'text-red-600 bg-red-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function isValidVIN(vin: string): boolean {
  // Basic VIN validation (17 characters, alphanumeric except I, O, Q)
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/;
  return vinRegex.test(vin.toUpperCase());
}

export function parseVIN(vin: string): {
  manufacturer?: string;
  year?: number;
  valid: boolean
} {
  if (!isValidVIN(vin)) {
    return { valid: false };
  }

  // Basic VIN parsing (simplified)
  const yearCode = vin.charAt(9);
  const yearMap: Record<string, number> = {
    'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014,
    'F': 2015, 'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019,
    'L': 2020, 'M': 2021, 'N': 2022, 'P': 2023, 'R': 2024
  };

  return {
    year: yearMap[yearCode],
    valid: true
  };
}

export function formatOdometer(value: number, unit: 'km' | 'miles' = 'km'): string {
  return `${value.toLocaleString()} ${unit}`;
}

export function getIMReadinessColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'ready':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'not_ready':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'not_applicable':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case 'error':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function calculateReadinessPercentage(ready: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((ready / total) * 100);
}

export function formatConfidenceScore(score: number): string {
  const percentage = Math.round(score * 100);
  if (percentage >= 90) return `${percentage}% (Excellent)`;
  if (percentage >= 80) return `${percentage}% (High)`;
  if (percentage >= 60) return `${percentage}% (Medium)`;
  if (percentage >= 40) return `${percentage}% (Low)`;
  return `${percentage}% (Very Low)`;
}

export function generateDTCSearchUrl(code: string, vehicleInfo?: any): string {
  const baseQuery = `${code} diagnostic trouble code`;
  const vehicleQuery = vehicleInfo
    ? ` ${vehicleInfo.make} ${vehicleInfo.model} ${vehicleInfo.year}`
    : '';
  return `https://www.google.com/search?q=${encodeURIComponent(baseQuery + vehicleQuery)}`;
}



export function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  return `${Math.floor(diffInSeconds / 86400)}d ago`;
}

export function validateExpertModeCode(code: string): boolean {
  // Simple validation - in production, this should be more secure
  return code.length >= 6 && /^[A-Z0-9]+$/.test(code.toUpperCase());
}
