'use client';

import { useState, useEffect } from 'react';
import { 
  Setting<PERSON>, 
  ArrowLeft, 
  Save, 
  RotateCcw, 
  Database,
  Wifi,
  Brain,
  Shield,
  Bell,
  Palette,
  Download,
  Upload,
  Trash2,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface AppSettings {
  // Connection Settings
  connection: {
    auto_connect: boolean;
    preferred_port: string;
    connection_timeout: number;
    retry_attempts: number;
    query_delay: number;
  };
  
  // AI Settings
  ai: {
    auto_analyze: boolean;
    confidence_threshold: number;
    include_cost_estimates: boolean;
    include_predictive_maintenance: boolean;
    fallback_enabled: boolean;
  };
  
  // UI Settings
  ui: {
    theme: 'light' | 'dark' | 'auto';
    notifications_enabled: boolean;
    sound_enabled: boolean;
    auto_refresh_interval: number;
    chart_update_interval: number;
  };
  
  // Expert Mode Settings
  expert: {
    unlock_code: string;
    session_timeout: number;
    require_confirmation: boolean;
    log_operations: boolean;
  };
  
  // Database Settings
  database: {
    auto_backup: boolean;
    backup_interval: number;
    max_history_entries: number;
    cleanup_old_entries: boolean;
  };
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeSection, setActiveSection] = useState('connection');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/v1/frontend/settings');
      const result = await response.json();
      
      if (result.success) {
        setSettings(result.data);
      } else {
        toast.error('Failed to load settings');
      }
    } catch (error) {
      toast.error('Network error loading settings');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;
    
    setIsSaving(true);
    
    try {
      const response = await fetch('/api/v1/frontend/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('Settings saved successfully');
        setHasChanges(false);
      } else {
        toast.error(result.error || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('Network error saving settings');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = async () => {
    if (!confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch('/api/v1/frontend/settings/reset', {
        method: 'POST'
      });
      
      const result = await response.json();
      
      if (result.success) {
        setSettings(result.data);
        setHasChanges(false);
        toast.success('Settings reset to defaults');
      } else {
        toast.error('Failed to reset settings');
      }
    } catch (error) {
      toast.error('Network error resetting settings');
    }
  };

  const exportSettings = async () => {
    try {
      const response = await fetch('/api/v1/frontend/settings/export');
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `obd2_settings_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Settings exported successfully');
    } catch (error) {
      toast.error('Export failed');
    }
  };

  const importSettings = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/v1/frontend/settings/import', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        setSettings(result.data);
        setHasChanges(false);
        toast.success('Settings imported successfully');
      } else {
        toast.error(result.error || 'Import failed');
      }
    } catch (error) {
      toast.error('Network error during import');
    }
  };

  const updateSetting = (section: keyof AppSettings, key: string, value: any) => {
    if (!settings) return;
    
    setSettings(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const sections = [
    { id: 'connection', name: 'Connection', icon: <Wifi className="w-4 h-4" /> },
    { id: 'ai', name: 'AI Engine', icon: <Brain className="w-4 h-4" /> },
    { id: 'ui', name: 'Interface', icon: <Palette className="w-4 h-4" /> },
    { id: 'expert', name: 'Expert Mode', icon: <Shield className="w-4 h-4" /> },
    { id: 'database', name: 'Database', icon: <Database className="w-4 h-4" /> }
  ];

  const renderConnectionSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Connection Settings</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Auto Connect
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.connection.auto_connect || false}
              onChange={(e) => updateSetting('connection', 'auto_connect', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Automatically connect to last used port
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Port
          </label>
          <input
            type="text"
            value={settings?.connection.preferred_port || ''}
            onChange={(e) => updateSetting('connection', 'preferred_port', e.target.value)}
            placeholder="e.g., /dev/ttyUSB0 or COM3"
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Connection Timeout (seconds)
          </label>
          <input
            type="number"
            min="5"
            max="60"
            value={settings?.connection.connection_timeout || 30}
            onChange={(e) => updateSetting('connection', 'connection_timeout', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Retry Attempts
          </label>
          <input
            type="number"
            min="1"
            max="10"
            value={settings?.connection.retry_attempts || 3}
            onChange={(e) => updateSetting('connection', 'retry_attempts', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Query Delay (milliseconds)
          </label>
          <input
            type="number"
            min="50"
            max="1000"
            step="50"
            value={settings?.connection.query_delay || 100}
            onChange={(e) => updateSetting('connection', 'query_delay', parseInt(e.target.value))}
            className="input"
          />
          <p className="text-xs text-gray-500 mt-1">
            Delay between OBD2 queries to prevent timeouts
          </p>
        </div>
      </div>
    </div>
  );

  const renderAISettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">AI Engine Settings</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Auto Analyze DTCs
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ai.auto_analyze || false}
              onChange={(e) => updateSetting('ai', 'auto_analyze', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Automatically analyze DTCs with AI
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            AI Fallback
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ai.fallback_enabled || false}
              onChange={(e) => updateSetting('ai', 'fallback_enabled', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Use AI for unknown DTC codes
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Confidence Threshold
          </label>
          <input
            type="range"
            min="0.1"
            max="1.0"
            step="0.1"
            value={settings?.ai.confidence_threshold || 0.7}
            onChange={(e) => updateSetting('ai', 'confidence_threshold', parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Low (0.1)</span>
            <span>Current: {settings?.ai.confidence_threshold || 0.7}</span>
            <span>High (1.0)</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Include Cost Estimates
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ai.include_cost_estimates || false}
              onChange={(e) => updateSetting('ai', 'include_cost_estimates', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Include repair cost estimates in AI analysis
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Predictive Maintenance
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ai.include_predictive_maintenance || false}
              onChange={(e) => updateSetting('ai', 'include_predictive_maintenance', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Include predictive maintenance suggestions
            </span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderUISettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Interface Settings</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Theme
          </label>
          <select
            value={settings?.ui.theme || 'light'}
            onChange={(e) => updateSetting('ui', 'theme', e.target.value)}
            className="input"
          >
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="auto">Auto (System)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notifications
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ui.notifications_enabled || false}
              onChange={(e) => updateSetting('ui', 'notifications_enabled', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Enable desktop notifications
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Sound Effects
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.ui.sound_enabled || false}
              onChange={(e) => updateSetting('ui', 'sound_enabled', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Enable sound effects
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Auto Refresh Interval (seconds)
          </label>
          <input
            type="number"
            min="5"
            max="300"
            value={settings?.ui.auto_refresh_interval || 30}
            onChange={(e) => updateSetting('ui', 'auto_refresh_interval', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chart Update Interval (milliseconds)
          </label>
          <input
            type="number"
            min="500"
            max="5000"
            step="500"
            value={settings?.ui.chart_update_interval || 1000}
            onChange={(e) => updateSetting('ui', 'chart_update_interval', parseInt(e.target.value))}
            className="input"
          />
        </div>
      </div>
    </div>
  );

  const renderExpertSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Expert Mode Settings</h3>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-yellow-600" />
          <span className="text-yellow-800 font-medium">
            Expert mode allows potentially dangerous operations. Use with caution.
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Unlock Code
          </label>
          <input
            type="password"
            value={settings?.expert.unlock_code || ''}
            onChange={(e) => updateSetting('expert', 'unlock_code', e.target.value)}
            placeholder="Enter expert mode unlock code"
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Session Timeout (minutes)
          </label>
          <input
            type="number"
            min="5"
            max="120"
            value={settings?.expert.session_timeout || 30}
            onChange={(e) => updateSetting('expert', 'session_timeout', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Require Confirmation
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.expert.require_confirmation || false}
              onChange={(e) => updateSetting('expert', 'require_confirmation', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Require confirmation for critical operations
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Log Operations
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.expert.log_operations || false}
              onChange={(e) => updateSetting('expert', 'log_operations', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Log all expert mode operations
            </span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Database Settings</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Auto Backup
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.database.auto_backup || false}
              onChange={(e) => updateSetting('database', 'auto_backup', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Automatically backup database
            </span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Backup Interval (hours)
          </label>
          <input
            type="number"
            min="1"
            max="168"
            value={settings?.database.backup_interval || 24}
            onChange={(e) => updateSetting('database', 'backup_interval', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Max History Entries
          </label>
          <input
            type="number"
            min="10"
            max="10000"
            value={settings?.database.max_history_entries || 1000}
            onChange={(e) => updateSetting('database', 'max_history_entries', parseInt(e.target.value))}
            className="input"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cleanup Old Entries
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings?.database.cleanup_old_entries || false}
              onChange={(e) => updateSetting('database', 'cleanup_old_entries', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">
              Automatically remove old entries
            </span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'connection': return renderConnectionSettings();
      case 'ai': return renderAISettings();
      case 'ui': return renderUISettings();
      case 'expert': return renderExpertSettings();
      case 'database': return renderDatabaseSettings();
      default: return renderConnectionSettings();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Settings className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="btn btn-secondary flex items-center space-x-2">
                <ArrowLeft className="w-4 h-4" />
                <span>Dashboard</span>
              </Link>
              <div className="flex items-center space-x-3">
                <Settings className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">Settings</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="file"
                accept=".json"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) importSettings(file);
                }}
                className="hidden"
                id="import-settings"
              />
              <label htmlFor="import-settings" className="btn btn-secondary flex items-center space-x-2 cursor-pointer">
                <Upload className="w-4 h-4" />
                <span>Import</span>
              </label>
              
              <button
                onClick={exportSettings}
                className="btn btn-secondary flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
              
              <button
                onClick={resetToDefaults}
                className="btn bg-red-600 text-white hover:bg-red-700 flex items-center space-x-2"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Reset</span>
              </button>
              
              <button
                onClick={saveSettings}
                disabled={!hasChanges || isSaving}
                className={cn(
                  'btn flex items-center space-x-2',
                  hasChanges ? 'btn-primary' : 'btn-secondary opacity-50'
                )}
              >
                {isSaving ? (
                  <Settings className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>Save</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Settings Categories</h3>
                <nav className="space-y-1">
                  {sections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        'w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors',
                        activeSection === section.id
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50'
                      )}
                    >
                      {section.icon}
                      <span>{section.name}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              {renderSectionContent()}
            </div>
          </div>
        </div>
      </div>

      {/* Changes Indicator */}
      {hasChanges && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
          <AlertTriangle className="w-4 h-4" />
          <span>You have unsaved changes</span>
        </div>
      )}
    </div>
  );
}
