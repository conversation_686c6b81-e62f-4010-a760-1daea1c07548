'use client';

import { useState, useEffect } from 'react';
import { 
  Car, 
  Activity, 
  BarChart3, 
  Settings, 
  Shield,
  Cpu,
  Snowf<PERSON>,
  Wrench,
  History,
  AlertTriangle,
  CheckCircle,
  Clock,
  Gauge,
  Zap
} from 'lucide-react';
import { ConnectionSelector } from '@/components/ConnectionSelector';
import { DTCAnalysis } from '@/components/DTCAnalysis';
import { LiveDataDashboard } from '@/components/LiveDataDashboard';
import { ExpertModePanel } from '@/components/ExpertModePanel';
import { FreezeFramePanel } from '@/components/FreezeFramePanel';
import { ServiceLightReset } from '@/components/ServiceLightReset';
import { ECUInfoPanel } from '@/components/ECUInfoPanel';
import { RealTimeCharts } from '@/components/RealTimeCharts';
import { VehicleInfoPanel } from '@/components/VehicleInfoPanel';
import { IMReadinessPanel } from '@/components/IMReadinessPanel';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { ConnectionStatus, DiagnosticSession, DTCCode, VehicleInfo, OperatingMode } from '@/types/diagnostic';
import { apiClient } from '@/lib/api';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

type DashboardTab = 
  | 'overview' 
  | 'diagnostics' 
  | 'live_data' 
  | 'charts' 
  | 'freeze_frame' 
  | 'service_reset' 
  | 'ecu_info' 
  | 'expert_mode';

interface DashboardStats {
  total_dtcs: number;
  critical_dtcs: number;
  last_scan_time: string;
  connection_uptime: number;
  ai_analyses_performed: number;
}

export default function DashboardPage() {
  // Connection state
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Dashboard state
  const [activeTab, setActiveTab] = useState<DashboardTab>('overview');
  const [operatingMode, setOperatingMode] = useState<OperatingMode>('safe');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [currentSession, setCurrentSession] = useState<DiagnosticSession | null>(null);
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo | null>(null);

  // Live data state
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [updateInterval, setUpdateInterval] = useState(1000);

  useEffect(() => {
    if (isConnected) {
      loadDashboardStats();
      loadCurrentSession();
    }
  }, [isConnected]);

  const loadDashboardStats = async () => {
    try {
      const response = await fetch('/api/v1/frontend/dashboard/stats');
      const result = await response.json();
      
      if (result.success) {
        setDashboardStats(result.data);
      }
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
    }
  };

  const loadCurrentSession = async () => {
    try {
      const response = await fetch('/api/v1/frontend/session/current');
      const result = await response.json();
      
      if (result.success) {
        setCurrentSession(result.data);
        setVehicleInfo(result.data.vehicle_info);
      }
    } catch (error) {
      console.error('Failed to load current session:', error);
    }
  };

  const handleConnectionSuccess = (status: ConnectionStatus) => {
    setConnectionStatus(status);
    setIsConnected(true);
    setVehicleInfo(status.vehicle_info || null);
    toast.success('Connected successfully!');
  };

  const handleConnectionError = (error: string) => {
    setIsConnected(false);
    setConnectionStatus(null);
    toast.error(error);
  };

  const handleDisconnect = async () => {
    try {
      await apiClient.disconnect();
      setIsConnected(false);
      setConnectionStatus(null);
      setCurrentSession(null);
      setVehicleInfo(null);
      setIsMonitoring(false);
      toast.success('Disconnected successfully');
    } catch (error) {
      toast.error('Failed to disconnect');
    }
  };

  const handleModeChange = (mode: OperatingMode) => {
    setOperatingMode(mode);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: <Gauge className="w-4 h-4" /> },
    { id: 'diagnostics', name: 'Diagnostics', icon: <AlertTriangle className="w-4 h-4" /> },
    { id: 'live_data', name: 'Live Data', icon: <Activity className="w-4 h-4" /> },
    { id: 'charts', name: 'Charts', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'freeze_frame', name: 'Freeze Frame', icon: <Snowflake className="w-4 h-4" /> },
    { id: 'service_reset', name: 'Service Reset', icon: <Wrench className="w-4 h-4" /> },
    { id: 'ecu_info', name: 'ECU Info', icon: <Cpu className="w-4 h-4" /> },
    { id: 'expert_mode', name: 'Expert Mode', icon: <Shield className="w-4 h-4" /> }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      {dashboardStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-red-100 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{dashboardStats.total_dtcs}</div>
                <div className="text-sm text-gray-600">Total DTCs</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-lg">
                <Zap className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{dashboardStats.critical_dtcs}</div>
                <div className="text-sm text-gray-600">Critical DTCs</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {Math.floor(dashboardStats.connection_uptime / 60)}m
                </div>
                <div className="text-sm text-gray-600">Uptime</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Activity className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{dashboardStats.ai_analyses_performed}</div>
                <div className="text-sm text-gray-600">AI Analyses</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vehicle Info */}
      {vehicleInfo && (
        <VehicleInfoPanel vehicleInfo={vehicleInfo} />
      )}

      {/* I/M Readiness */}
      <IMReadinessPanel />

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('diagnostics')}
            className="btn btn-primary flex flex-col items-center space-y-2 py-4"
          >
            <AlertTriangle className="w-6 h-6" />
            <span>Run Diagnostics</span>
          </button>
          
          <button
            onClick={() => setActiveTab('live_data')}
            className="btn btn-secondary flex flex-col items-center space-y-2 py-4"
          >
            <Activity className="w-6 h-6" />
            <span>Live Data</span>
          </button>
          
          <button
            onClick={() => setActiveTab('service_reset')}
            className="btn btn-secondary flex flex-col items-center space-y-2 py-4"
          >
            <Wrench className="w-6 h-6" />
            <span>Service Reset</span>
          </button>
          
          <button
            onClick={() => setActiveTab('charts')}
            className="btn btn-secondary flex flex-col items-center space-y-2 py-4"
          >
            <BarChart3 className="w-6 h-6" />
            <span>View Charts</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      
      case 'diagnostics':
        return (
          <DTCAnalysis
            dtcCodes={currentSession?.dtc_codes || []}
            vehicleInfo={vehicleInfo || undefined}
            operatingMode={operatingMode}
            onModeChange={handleModeChange}
          />
        );
      
      case 'live_data':
        return (
          <LiveDataDashboard
            isExpertMode={operatingMode === 'expert'}
            onExpertModeToggle={(expert) => setOperatingMode(expert ? 'expert' : 'safe')}
          />
        );
      
      case 'charts':
        return (
          <RealTimeCharts
            isMonitoring={isMonitoring}
            onMonitoringToggle={setIsMonitoring}
            updateInterval={updateInterval}
            onIntervalChange={setUpdateInterval}
          />
        );
      
      case 'freeze_frame':
        return <FreezeFramePanel />;
      
      case 'service_reset':
        return (
          <ServiceLightReset
            isExpertMode={operatingMode === 'expert'}
            vehicleInfo={vehicleInfo || undefined}
          />
        );
      
      case 'ecu_info':
        return <ECUInfoPanel isExpertMode={operatingMode === 'expert'} />;
      
      case 'expert_mode':
        return (
          <ExpertModePanel
            isActive={operatingMode === 'expert'}
            onActivate={(active) => setOperatingMode(active ? 'expert' : 'safe')}
          />
        );
      
      default:
        return renderOverview();
    }
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-gray-50">
        <ConnectionSelector
          onConnectionSuccess={handleConnectionSuccess}
          onConnectionError={handleConnectionError}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Car className="w-8 h-8 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                OBD2 AI Diagnostic Dashboard
              </h1>
              {connectionStatus && (
                <div className="flex items-center space-x-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">
                    Connected via {connectionStatus.port}
                  </span>
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Operating Mode Indicator */}
              <div className={cn(
                'px-3 py-1 rounded-full text-sm font-medium',
                operatingMode === 'expert' 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-green-100 text-green-800'
              )}>
                {operatingMode === 'expert' ? 'Expert Mode' : 'Safe Mode'}
              </div>
              
              <button
                onClick={handleDisconnect}
                className="btn btn-secondary"
              >
                Disconnect
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as DashboardTab)}
                className={cn(
                  'flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                {tab.icon}
                <span>{tab.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}
