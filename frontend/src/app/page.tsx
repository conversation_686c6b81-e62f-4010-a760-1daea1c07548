'use client';

import { useState, useEffect } from 'react';
import {
  Car,
  Activity,
  BarChart3,
  History,
  Settings,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Zap,
  Shield
} from 'lucide-react';
import { ConnectionSelector } from '@/components/ConnectionSelector';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { ConnectionStatus } from '@/types/diagnostic';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface QuickStats {
  total_sessions: number;
  total_dtcs_found: number;
  last_scan_date: string;
  ai_analyses_performed: number;
}

export default function HomePage() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [quickStats, setQuickStats] = useState<QuickStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  useEffect(() => {
    loadQuickStats();
  }, []);

  const loadQuickStats = async () => {
    setIsLoadingStats(true);
    try {
      const response = await fetch('/api/v1/frontend/stats/quick');
      const result = await response.json();

      if (result.success) {
        setQuickStats(result.data);
      }
    } catch (error) {
      console.error('Failed to load quick stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const handleConnectionSuccess = (status: ConnectionStatus) => {
    setConnectionStatus(status);
    setIsConnected(true);
    toast.success('Connected successfully! Redirecting to dashboard...');

    // Redirect to dashboard after successful connection
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 1500);
  };

  const handleConnectionError = (error: string) => {
    toast.error(error);
  };

  const features = [
    {
      icon: <AlertTriangle className="w-8 h-8 text-red-600" />,
      title: 'DTC Diagnostics',
      description: 'Read and analyze diagnostic trouble codes with AI-powered explanations',
      color: 'red'
    },
    {
      icon: <Activity className="w-8 h-8 text-blue-600" />,
      title: 'Live Data Monitoring',
      description: 'Real-time sensor data monitoring with fuel mixture analysis',
      color: 'blue'
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-600" />,
      title: 'Real-Time Charts',
      description: 'Visualize sensor data with interactive charts and graphs',
      color: 'green'
    },
    {
      icon: <Shield className="w-8 h-8 text-purple-600" />,
      title: 'Expert Mode',
      description: 'Advanced features for professional diagnostics and ECU operations',
      color: 'purple'
    }
  ];

  if (isConnected) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Connection Successful!
          </h2>
          <p className="text-gray-600 mb-4">
            Redirecting to dashboard...
          </p>
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <Car className="w-12 h-12 text-blue-600 mr-4" />
              <h1 className="text-4xl font-bold text-gray-900">
                OBD2 AI Diagnostic System
              </h1>
            </div>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Professional automotive diagnostics powered by artificial intelligence.
              Connect to your vehicle and get instant, intelligent analysis of diagnostic trouble codes.
            </p>

            {/* Quick Stats */}
            {quickStats && !isLoadingStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 max-w-2xl mx-auto">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">{quickStats.total_sessions}</div>
                  <div className="text-sm text-gray-600">Total Sessions</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">{quickStats.total_dtcs_found}</div>
                  <div className="text-sm text-gray-600">DTCs Found</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">{quickStats.ai_analyses_performed}</div>
                  <div className="text-sm text-gray-600">AI Analyses</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {quickStats.last_scan_date ? new Date(quickStats.last_scan_date).toLocaleDateString() : 'Never'}
                  </div>
                  <div className="text-sm text-gray-600">Last Scan</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Powerful Diagnostic Features
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border p-6 text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Connection Section */}
      <div className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <ConnectionSelector
            onConnectionSuccess={handleConnectionSuccess}
            onConnectionError={handleConnectionError}
          />
        </div>
      </div>

      {/* Quick Access Section */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Quick Access
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/history" className="group">
              <div className="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors">
                <History className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Diagnostic History
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  View and manage your previous diagnostic sessions
                </p>
                <div className="flex items-center justify-center text-blue-600 group-hover:text-blue-700">
                  <span className="mr-2">View History</span>
                  <ArrowRight className="w-4 h-4" />
                </div>
              </div>
            </Link>

            <Link href="/settings" className="group">
              <div className="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors">
                <Settings className="w-8 h-8 text-gray-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Settings
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Configure connection, AI, and interface settings
                </p>
                <div className="flex items-center justify-center text-blue-600 group-hover:text-blue-700">
                  <span className="mr-2">Open Settings</span>
                  <ArrowRight className="w-4 h-4" />
                </div>
              </div>
            </Link>

            <Link href="/enhanced" className="group">
              <div className="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors">
                <Zap className="w-8 h-8 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Enhanced Features
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Access advanced diagnostic tools and expert mode
                </p>
                <div className="flex items-center justify-center text-blue-600 group-hover:text-blue-700">
                  <span className="mr-2">Explore</span>
                  <ArrowRight className="w-4 h-4" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <Car className="w-6 h-6 mr-2" />
            <span className="text-lg font-semibold">OBD2 AI Diagnostic System</span>
          </div>
          <p className="text-gray-400">
            Professional automotive diagnostics with AI-powered analysis
          </p>
        </div>
      </footer>
    </div>
  );
}
