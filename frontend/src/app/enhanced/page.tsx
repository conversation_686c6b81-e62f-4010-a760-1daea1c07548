'use client';

import { useState, useEffect } from 'react';
import { 
  Car, 
  Activity, 
  Settings, 
  BarChart3, 
  Shield,
  Gauge,
  Brain,
  AlertTriangle
} from 'lucide-react';
import { ConnectionSelector } from '@/components/ConnectionSelector';
import { DiagnosticResults } from '@/components/DiagnosticResults';
import { LiveDataDashboard } from '@/components/LiveDataDashboard';
import { ExpertModePanel } from '@/components/ExpertModePanel';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { FreezeFramePanel } from '@/components/FreezeFramePanel';
import { ServiceLightReset } from '@/components/ServiceLightReset';
import { ECUInfoPanel } from '@/components/ECUInfoPanel';
import { RealTimeCharts } from '@/components/RealTimeCharts';
import { DiagnosticSession, ConnectionStatus, ScanOptions } from '@/types/diagnostic';
import { apiClient } from '@/lib/api';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

type AppState = 'connection' | 'scanning' | 'results' | 'live-data' | 'expert' | 'error';

export default function EnhancedPage() {
  const [appState, setAppState] = useState<AppState>('connection');
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [currentSession, setCurrentSession] = useState<DiagnosticSession | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [scanProgress, setScanProgress] = useState(0);
  const [isExpertMode, setIsExpertMode] = useState(false);

  // Check for existing connection on mount
  useEffect(() => {
    checkExistingConnection();
    checkExpertMode();
  }, []);

  const checkExistingConnection = async () => {
    try {
      const response = await apiClient.getConnectionStatus();
      if (response.success && response.data?.connected) {
        setConnectionStatus(response.data);
      }
    } catch (error) {
      console.log('No existing connection found');
    }
  };

  const checkExpertMode = () => {
    const token = localStorage.getItem('expert_session_token');
    if (token) {
      // Verify token is still valid
      fetch('/api/v1/enhanced/expert/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ session_token: token })
      })
      .then(res => res.json())
      .then(result => {
        if (result.success && result.data.valid) {
          setIsExpertMode(true);
        } else {
          localStorage.removeItem('expert_session_token');
        }
      })
      .catch(() => {
        localStorage.removeItem('expert_session_token');
      });
    }
  };

  const handleConnectionSuccess = async (connectionData: ConnectionStatus) => {
    setConnectionStatus(connectionData);
    setError(null);
    
    // Show navigation options instead of auto-starting scan
    if (connectionData.connection_type === 'mock') {
      // For mock data, go directly to live data
      setAppState('live-data');
      toast.success('Mock connection established - Live data available');
    } else {
      // For real connections, start with diagnostic scan
      await startDiagnosticScan();
    }
  };

  const handleConnectionError = (errorMessage: string) => {
    setError(errorMessage);
    setAppState('error');
  };

  const startDiagnosticScan = async () => {
    setAppState('scanning');
    setScanProgress(0);

    try {
      const scanOptions: ScanOptions = {
        include_freeze_frame: true,
        include_pending_codes: true,
        include_permanent_codes: true,
        read_live_data: true,
        ai_analysis: true,
        include_im_readiness: true,
        include_vehicle_info: true,
        include_odometer: true,
      };

      const response = await apiClient.startDiagnosticScan(scanOptions);
      
      if (response.success && response.data?.session_id) {
        pollSessionStatus(response.data.session_id);
      } else {
        throw new Error(response.error || 'Failed to start diagnostic scan');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Scan failed';
      toast.error(errorMessage);
      setError(errorMessage);
      setAppState('error');
    }
  };

  const pollSessionStatus = async (sessionId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await apiClient.getDiagnosticSession(sessionId);
        
        if (response.success && response.data) {
          const session = response.data;
          setCurrentSession(session);

          switch (session.status) {
            case 'connecting':
              setScanProgress(10);
              break;
            case 'scanning':
              setScanProgress(50);
              break;
            case 'analyzing':
              setScanProgress(80);
              break;
            case 'completed':
              setScanProgress(100);
              setAppState('results');
              clearInterval(pollInterval);
              toast.success('Diagnostic scan completed!');
              break;
            case 'error':
              setError(session.error_message || 'Scan failed');
              setAppState('error');
              clearInterval(pollInterval);
              break;
          }
        }
      } catch (error) {
        console.error('Error polling session status:', error);
        clearInterval(pollInterval);
        setError('Lost connection to diagnostic session');
        setAppState('error');
      }
    }, 2000);

    setTimeout(() => clearInterval(pollInterval), 300000);
  };

  const handleNewScan = () => {
    setCurrentSession(null);
    setScanProgress(0);
    startDiagnosticScan();
  };

  const handleDisconnect = async () => {
    try {
      await apiClient.disconnect();
      setConnectionStatus(null);
      setCurrentSession(null);
      setAppState('connection');
      toast.success('Disconnected successfully');
    } catch (error) {
      toast.error('Failed to disconnect');
    }
  };

  const handleRetry = () => {
    setError(null);
    setAppState('connection');
  };

  const handleExpertModeToggle = (active: boolean) => {
    setIsExpertMode(active);
    if (active) {
      toast.success('Expert mode activated');
    } else {
      toast.success('Expert mode deactivated');
    }
  };

  const renderNavigation = () => {
    if (!connectionStatus?.connected) return null;

    const navItems = [
      { id: 'results', label: 'Diagnostics', icon: Car, available: !!currentSession },
      { id: 'live-data', label: 'Live Data', icon: Activity, available: true },
      { id: 'expert', label: 'Expert Mode', icon: Shield, available: true },
    ];

    return (
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <Gauge className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">OBD2 Diagnostics</h1>
              </div>
              
              <nav className="flex space-x-4">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => item.available && setAppState(item.id as AppState)}
                    disabled={!item.available}
                    className={cn(
                      'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                      appState === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : item.available
                        ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                        : 'text-gray-400 cursor-not-allowed'
                    )}
                  >
                    <item.icon className="w-4 h-4" />
                    <span>{item.label}</span>
                    {item.id === 'expert' && isExpertMode && (
                      <div className="w-2 h-2 bg-red-500 rounded-full" />
                    )}
                  </button>
                ))}
              </nav>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                {connectionStatus.connection_type === 'mock' ? 'Mock Data' : 'Live Connection'}
              </div>
              <button
                onClick={handleDisconnect}
                className="text-sm text-red-600 hover:text-red-700"
              >
                Disconnect
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (appState) {
      case 'connection':
        return (
          <div className="max-w-4xl mx-auto p-6">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Enhanced OBD2 Diagnostic System
              </h1>
              <p className="text-lg text-gray-600">
                Advanced diagnostics with AI analysis, live data monitoring, and expert mode
              </p>
            </div>
            <ConnectionSelector
              onConnectionSuccess={handleConnectionSuccess}
              onConnectionError={handleConnectionError}
            />
          </div>
        );

      case 'scanning':
        return (
          <div className="max-w-2xl mx-auto p-6">
            <div className="card text-center py-12">
              <div className="mb-8">
                <LoadingSpinner size="xl" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Scanning Vehicle
              </h2>
              <p className="text-gray-600 mb-6">
                {currentSession?.status === 'connecting' && 'Establishing connection to vehicle...'}
                {currentSession?.status === 'scanning' && 'Reading diagnostic trouble codes...'}
                {currentSession?.status === 'analyzing' && 'Analyzing results with AI...'}
                {!currentSession && 'Initializing diagnostic scan...'}
              </p>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${scanProgress}%` }}
                />
              </div>
              <div className="text-sm text-gray-500">
                {scanProgress}% Complete
              </div>
            </div>
          </div>
        );

      case 'results':
        return currentSession ? (
          <div className="max-w-7xl mx-auto p-6">
            <DiagnosticResults
              session={currentSession}
              onNewScan={handleNewScan}
              onDisconnect={handleDisconnect}
            />
          </div>
        ) : (
          <div className="max-w-2xl mx-auto p-6">
            <div className="card text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                No Results Available
              </h2>
              <button onClick={handleNewScan} className="btn-primary">
                Start New Scan
              </button>
            </div>
          </div>
        );

      case 'live-data':
        return (
          <div className="max-w-7xl mx-auto p-6">
            <LiveDataDashboard
              isExpertMode={isExpertMode}
              onExpertModeToggle={() => setAppState('expert')}
            />
          </div>
        );

      case 'expert':
        return (
          <div className="max-w-4xl mx-auto p-6">
            <ExpertModePanel
              isActive={isExpertMode}
              onActivate={handleExpertModeToggle}
            />
          </div>
        );

      case 'error':
        return (
          <div className="max-w-2xl mx-auto p-6">
            <div className="card text-center py-12">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Connection Error
              </h2>
              <p className="text-gray-600 mb-6">
                {error || 'An unexpected error occurred'}
              </p>
              <div className="flex justify-center space-x-4">
                <button onClick={handleRetry} className="btn-primary">
                  Try Again
                </button>
                <button onClick={handleDisconnect} className="btn-secondary">
                  Disconnect
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {renderNavigation()}
      {renderContent()}
    </div>
  );
}
