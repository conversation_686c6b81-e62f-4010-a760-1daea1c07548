'use client';

import { useState } from 'react';
import { 
  History, 
  ArrowLeft, 
  Download, 
  Upload,
  Trash2,
  FileText,
  BarChart3,
  Calendar,
  Car
} from 'lucide-react';
import { DiagnosticHistory } from '@/components/DiagnosticHistory';
import { DTCAnalysis } from '@/components/DTCAnalysis';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface HistorySession {
  id: string;
  timestamp: string;
  vehicle_info: {
    make: string;
    model: string;
    year: number;
    vin?: string;
  };
  dtc_codes: any[];
  ai_analysis?: any;
  session_data: any;
}

export default function HistoryPage() {
  const [selectedSession, setSelectedSession] = useState<HistorySession | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);

  const handleSessionSelect = async (sessionId: string) => {
    setIsLoadingSession(true);
    
    try {
      const response = await fetch(`/api/v1/frontend/history/sessions/${sessionId}`);
      const result = await response.json();
      
      if (result.success) {
        setSelectedSession(result.data);
      } else {
        toast.error('Failed to load session details');
      }
    } catch (error) {
      toast.error('Network error loading session');
    } finally {
      setIsLoadingSession(false);
    }
  };

  const handleExportSession = (sessionId: string) => {
    toast.success('Session exported successfully');
  };

  const handleImportSessions = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/v1/frontend/history/import', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Imported ${result.data.imported_count} sessions`);
        setShowImportDialog(false);
        // Refresh the history list
        window.location.reload();
      } else {
        toast.error(result.error || 'Import failed');
      }
    } catch (error) {
      toast.error('Network error during import');
    }
  };

  const handleExportAll = async () => {
    try {
      const response = await fetch('/api/v1/frontend/history/export-all');
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `diagnostic_history_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('History exported successfully');
    } catch (error) {
      toast.error('Export failed');
    }
  };

  const handleClearHistory = async () => {
    if (!confirm('Are you sure you want to clear all diagnostic history? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch('/api/v1/frontend/history/clear', {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('History cleared successfully');
        setSelectedSession(null);
        window.location.reload();
      } else {
        toast.error('Failed to clear history');
      }
    } catch (error) {
      toast.error('Network error clearing history');
    }
  };

  if (selectedSession) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSelectedSession(null)}
                  className="btn btn-secondary flex items-center space-x-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back to History</span>
                </button>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    Session Details
                  </h1>
                  <p className="text-sm text-gray-600">
                    {new Date(selectedSession.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleExportSession(selectedSession.id)}
                  className="btn btn-primary flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Export Session</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Session Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {isLoadingSession ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner />
              <span className="ml-3 text-gray-600">Loading session details...</span>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Session Info */}
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Session Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <span className="text-sm text-gray-600">Vehicle:</span>
                    <div className="font-medium">
                      {selectedSession.vehicle_info.year} {selectedSession.vehicle_info.make} {selectedSession.vehicle_info.model}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Date:</span>
                    <div className="font-medium">
                      {new Date(selectedSession.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Time:</span>
                    <div className="font-medium">
                      {new Date(selectedSession.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">DTCs Found:</span>
                    <div className="font-medium">{selectedSession.dtc_codes.length}</div>
                  </div>
                </div>
                
                {selectedSession.vehicle_info.vin && (
                  <div className="mt-4">
                    <span className="text-sm text-gray-600">VIN:</span>
                    <div className="font-medium font-mono">{selectedSession.vehicle_info.vin}</div>
                  </div>
                )}
              </div>

              {/* DTC Analysis */}
              <DTCAnalysis
                dtcCodes={selectedSession.dtc_codes}
                vehicleInfo={selectedSession.vehicle_info}
                onAnalysisComplete={(analysis) => {
                  console.log('Historical analysis:', analysis);
                }}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="btn btn-secondary flex items-center space-x-2">
                <ArrowLeft className="w-4 h-4" />
                <span>Dashboard</span>
              </Link>
              <div className="flex items-center space-x-3">
                <History className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">
                  Diagnostic History
                </h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowImportDialog(true)}
                className="btn btn-secondary flex items-center space-x-2"
              >
                <Upload className="w-4 h-4" />
                <span>Import</span>
              </button>
              
              <button
                onClick={handleExportAll}
                className="btn btn-primary flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export All</span>
              </button>
              
              <button
                onClick={handleClearHistory}
                className="btn bg-red-600 text-white hover:bg-red-700 flex items-center space-x-2"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear All</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DiagnosticHistory
          onSessionSelect={handleSessionSelect}
          onExportSession={handleExportSession}
        />
      </div>

      {/* Import Dialog */}
      {showImportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <Upload className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">Import Diagnostic History</h3>
            </div>
            
            <p className="text-gray-600 mb-4">
              Select a JSON file containing exported diagnostic sessions to import.
            </p>
            
            <div className="mb-4">
              <input
                type="file"
                accept=".json"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleImportSessions(file);
                  }
                }}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowImportDialog(false)}
                className="btn btn-secondary flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
