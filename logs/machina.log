2025-06-06 16:56:00.659 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 16:56:00.679 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 16:56:00.680 | WARNING  | app.main:lifespan:55 - Database seeding warning: Could not determine join condition between parent/child tables on relationship DTCCode.diagnostic_sessions - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.
2025-06-06 16:56:00.680 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 16:56:47.356 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 16:56:47.358 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 16:56:59.077 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 16:56:59.081 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 16:56:59.081 | WARNING  | app.main:lifespan:55 - Database seeding warning: Could not determine join condition between parent/child tables on relationship DTCCode.diagnostic_sessions - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.
2025-06-06 16:56:59.081 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 17:44:51.410 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 17:44:51.437 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 17:45:32.897 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 17:45:32.901 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 17:45:32.916 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 17:45:32.916 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:25:17.329 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:25:17.333 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:25:17.347 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:25:17.347 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:25:17.349 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:25:17.349 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:25:40.012 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:25:40.014 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:25:40.029 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:25:40.029 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:32:42.197 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:32:42.209 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:33:04.032 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:33:04.034 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:33:04.047 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:33:04.047 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:33:41.897 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:33:41.907 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:33:45.436 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:33:45.439 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:33:45.450 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:33:45.450 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:34:16.360 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:34:16.372 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:34:19.841 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:34:19.844 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:34:19.855 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:34:19.855 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:34:40.564 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:34:40.565 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:34:43.980 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:34:43.982 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:34:43.992 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:34:43.992 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:35:16.625 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:35:16.645 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:35:20.123 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:35:20.126 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:35:20.136 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:35:20.136 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:37:45.651 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:37:45.657 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:37:49.226 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:37:49.228 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:37:49.242 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:37:49.243 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:45:56.225 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:45:56.240 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:46:00.079 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:46:00.082 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:46:00.089 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:46:00.089 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:46:06.154 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:46:06.155 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:46:09.661 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:46:09.664 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:46:09.672 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:46:09.672 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:46:19.968 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:46:19.969 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:46:23.645 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:46:23.651 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:46:23.659 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:46:23.659 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:46:57.477 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:46:57.480 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:47:01.240 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:47:01.243 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:47:01.252 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:47:01.252 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:47:07.019 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:47:07.020 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:47:10.072 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:47:10.075 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:47:10.082 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:47:10.082 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:47:19.282 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:47:19.283 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:47:22.489 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:47:22.492 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:47:22.499 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:47:22.499 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:48:29.915 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:48:29.917 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:48:33.250 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:48:33.252 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:48:33.259 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:48:33.259 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:49:09.247 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:49:09.251 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:49:12.789 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:49:12.792 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:49:12.800 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:49:12.800 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:49:17.042 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:49:17.042 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:49:20.158 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:49:20.161 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:49:20.168 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:49:20.168 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:50:17.387 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:50:17.389 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:50:21.186 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:50:21.189 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:50:21.197 | WARNING  | app.main:lifespan:55 - Database seeding warning: (sqlite3.OperationalError) no such column: dtc_codes.source
[SQL: SELECT count(*) AS count_1 
FROM (SELECT dtc_codes.id AS dtc_codes_id, dtc_codes.code AS dtc_codes_code, dtc_codes.description AS dtc_codes_description, dtc_codes.category AS dtc_codes_category, dtc_codes.severity AS dtc_codes_severity, dtc_codes.system AS dtc_codes_system, dtc_codes.source AS dtc_codes_source, dtc_codes.trust_score AS dtc_codes_trust_score, dtc_codes.possible_causes AS dtc_codes_possible_causes, dtc_codes.repair_hints AS dtc_codes_repair_hints, dtc_codes.related_pids AS dtc_codes_related_pids, dtc_codes.freeze_frame_required AS dtc_codes_freeze_frame_required, dtc_codes.obd2_standard AS dtc_codes_obd2_standard, dtc_codes.brand_specific_info AS dtc_codes_brand_specific_info, dtc_codes.created_at AS dtc_codes_created_at, dtc_codes.updated_at AS dtc_codes_updated_at 
FROM dtc_codes) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-06 18:50:21.197 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:52:27.630 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:52:27.638 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:56:58.211 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:56:58.211 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:56:58.214 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:56:58.214 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:56:58.226 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:56:58.226 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:56:58.226 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:56:58.226 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 18:57:00.049 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 18:57:00.050 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 18:57:10.867 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 18:57:10.869 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 18:57:10.882 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 18:57:10.882 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:18:30.732 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:18:30.758 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:18:31.431 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:18:31.473 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:18:33.459 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:18:33.489 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:27:24.019 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:27:24.019 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:27:24.037 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:27:24.037 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:27:24.132 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:27:24.132 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:27:24.132 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:27:24.132 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:30:35.203 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:30:35.203 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:30:35.216 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:30:35.216 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:30:38.608 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:30:38.608 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:30:38.610 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:30:38.610 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:30:38.630 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:30:38.630 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:30:38.630 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:30:38.630 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:36:53.839 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:36:53.839 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:36:53.847 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:36:53.847 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:36:57.535 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:36:57.535 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:36:57.539 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:36:57.539 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:36:57.558 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:36:57.558 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:36:57.559 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:36:57.559 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:18.071 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:18.071 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:18.074 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:18.074 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:21.643 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:21.643 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:21.646 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:21.646 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:21.665 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:21.665 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:21.665 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:21.665 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:30.653 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:30.653 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:30.655 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:30.655 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:34.209 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:34.209 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:34.212 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:34.212 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:34.230 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:34.230 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:34.231 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:34.231 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:42.632 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:42.632 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:42.633 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:42.633 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:46.089 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:46.089 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:37:46.092 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:46.092 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:37:46.111 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:46.111 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:37:46.112 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:46.112 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:37:56.528 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:56.528 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:37:56.529 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:37:56.529 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:38:01.575 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:01.575 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:01.579 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:01.579 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:01.610 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:01.610 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:01.610 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:38:01.610 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:38:15.249 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:38:15.249 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:38:15.252 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:38:15.252 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:38:19.122 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:19.122 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:19.126 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:19.126 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:19.149 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:19.149 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:19.149 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:38:19.149 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:38:28.841 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:38:28.841 | INFO     | app.main:lifespan:65 - Shutting down OBD2 AI Diagnostic System
2025-06-06 19:38:28.841 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:38:28.841 | INFO     | app.main:lifespan:73 - Cleanup completed
2025-06-06 19:38:32.846 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:32.846 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:38:32.851 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:32.851 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:38:32.891 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:32.891 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:38:32.891 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:38:32.891 | INFO     | app.main:lifespan:57 - System initialization completed
2025-06-06 19:41:22.344 | INFO     | app.main:lifespan:39 - Starting OBD2 AI Diagnostic System
2025-06-06 19:41:22.348 | INFO     | app.main:lifespan:48 - Database initialized
2025-06-06 19:41:22.385 | INFO     | app.main:lifespan:53 - Database seeded with initial data
2025-06-06 19:41:22.385 | INFO     | app.main:lifespan:57 - System initialization completed
