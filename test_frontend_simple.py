#!/usr/bin/env python3
"""
Simple Frontend Test Script
Tests if all frontend components and pages exist
"""

import os
from pathlib import Path

def test_frontend_structure():
    """Test if all required frontend files exist"""
    print("🧪 FRONTEND STRUCTURE TEST")
    print("=" * 50)
    
    # Component files to check
    components = [
        "frontend/src/components/DTCAnalysis.tsx",
        "frontend/src/components/LiveDataDashboard.tsx", 
        "frontend/src/components/ExpertModePanel.tsx",
        "frontend/src/components/FreezeFramePanel.tsx",
        "frontend/src/components/ServiceLightReset.tsx",
        "frontend/src/components/ECUInfoPanel.tsx",
        "frontend/src/components/DiagnosticHistory.tsx",
        "frontend/src/components/RealTimeCharts.tsx",
        "frontend/src/components/ConnectionSelector.tsx",
        "frontend/src/components/VehicleInfoPanel.tsx",
        "frontend/src/components/IMReadinessPanel.tsx",
        "frontend/src/components/ConfirmationDialog.tsx",
        "frontend/src/components/NotificationSystem.tsx",
        "frontend/src/components/LoadingSpinner.tsx",
        "frontend/src/components/DiagnosticResults.tsx",
        "frontend/src/components/EnhancedDashboard.tsx",
    ]
    
    # Page files to check
    pages = [
        "frontend/src/app/page.tsx",
        "frontend/src/app/dashboard/page.tsx", 
        "frontend/src/app/history/page.tsx",
        "frontend/src/app/settings/page.tsx",
        "frontend/src/app/enhanced/page.tsx",
        "frontend/src/app/layout.tsx",
        "frontend/src/app/globals.css",
    ]
    
    # Type definitions
    types = [
        "frontend/src/types/diagnostic.ts",
    ]
    
    # Utility files
    utils = [
        "frontend/src/lib/api.ts",
        "frontend/src/lib/utils.ts",
    ]
    
    # Configuration files
    config = [
        "frontend/package.json",
        "frontend/next.config.js",
        "frontend/tailwind.config.js",
        "frontend/tsconfig.json",
    ]
    
    all_files = {
        "Components": components,
        "Pages": pages, 
        "Types": types,
        "Utils": utils,
        "Config": config
    }
    
    total_files = 0
    total_found = 0
    
    for category, files in all_files.items():
        print(f"\n📁 {category}:")
        found = 0
        for file_path in files:
            if Path(file_path).exists():
                print(f"  ✅ {file_path}")
                found += 1
            else:
                print(f"  ❌ {file_path} - MISSING")
            total_files += 1
        
        total_found += found
        print(f"  📊 {found}/{len(files)} files found")
    
    print(f"\n" + "=" * 50)
    print(f"📊 OVERALL SUMMARY")
    print(f"Total files checked: {total_files}")
    print(f"Files found: {total_found}")
    print(f"Files missing: {total_files - total_found}")
    print(f"Success rate: {(total_found/total_files)*100:.1f}%")
    
    if total_found == total_files:
        print("🎉 ALL FRONTEND FILES PRESENT!")
        return True
    else:
        print("⚠️  Some frontend files are missing")
        return False

def check_car_diagnosis_features():
    """Check if all car diagnosis features are implemented"""
    print("\n🚗 CAR DIAGNOSIS FEATURES CHECK")
    print("=" * 50)
    
    features = {
        "DTC Analysis": "frontend/src/components/DTCAnalysis.tsx",
        "Live Data Monitoring": "frontend/src/components/LiveDataDashboard.tsx",
        "Real-Time Charts": "frontend/src/components/RealTimeCharts.tsx", 
        "Freeze Frame Data": "frontend/src/components/FreezeFramePanel.tsx",
        "Service Light Reset": "frontend/src/components/ServiceLightReset.tsx",
        "ECU Information": "frontend/src/components/ECUInfoPanel.tsx",
        "Expert Mode": "frontend/src/components/ExpertModePanel.tsx",
        "Diagnostic History": "frontend/src/components/DiagnosticHistory.tsx",
        "Vehicle Info Panel": "frontend/src/components/VehicleInfoPanel.tsx",
        "I/M Readiness": "frontend/src/components/IMReadinessPanel.tsx",
        "Connection Management": "frontend/src/components/ConnectionSelector.tsx",
        "Dashboard": "frontend/src/app/dashboard/page.tsx",
        "Settings": "frontend/src/app/settings/page.tsx",
    }
    
    implemented = 0
    for feature, file_path in features.items():
        if Path(file_path).exists():
            print(f"✅ {feature}")
            implemented += 1
        else:
            print(f"❌ {feature} - NOT IMPLEMENTED")
    
    print(f"\n📊 Features implemented: {implemented}/{len(features)}")
    print(f"Implementation rate: {(implemented/len(features))*100:.1f}%")
    
    if implemented == len(features):
        print("🎉 ALL CAR DIAGNOSIS FEATURES IMPLEMENTED!")
        return True
    else:
        print("⚠️  Some features are missing")
        return False

def check_backend_integration():
    """Check if backend integration files exist"""
    print("\n🔗 BACKEND INTEGRATION CHECK")
    print("=" * 50)
    
    integration_files = [
        "frontend/src/lib/api.ts",
        "frontend/src/types/diagnostic.ts",
    ]
    
    found = 0
    for file_path in integration_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
            found += 1
        else:
            print(f"❌ {file_path} - MISSING")
    
    print(f"\n📊 Integration files: {found}/{len(integration_files)}")
    
    if found == len(integration_files):
        print("🎉 BACKEND INTEGRATION FILES PRESENT!")
        return True
    else:
        print("⚠️  Some integration files are missing")
        return False

def main():
    print("🚀 FRONTEND COMPLETENESS TEST")
    print("=" * 60)
    
    # Run all tests
    structure_ok = test_frontend_structure()
    features_ok = check_car_diagnosis_features()
    integration_ok = check_backend_integration()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    print(f"✅ Frontend Structure: {'PASS' if structure_ok else 'FAIL'}")
    print(f"✅ Car Diagnosis Features: {'PASS' if features_ok else 'FAIL'}")
    print(f"✅ Backend Integration: {'PASS' if integration_ok else 'FAIL'}")
    
    overall_success = all([structure_ok, features_ok, integration_ok])
    print(f"\n🎯 OVERALL RESULT: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("\n🎉 FRONTEND IS COMPLETE!")
        print("✨ All car diagnosis features are implemented")
        print("🔗 Backend integration is ready")
        print("📱 All UI components are present")
        print("\n🚀 Ready to start development servers:")
        print("   Backend: uvicorn app.main:app --reload")
        print("   Frontend: cd frontend && npm run dev")
    else:
        print("\n⚠️  Frontend needs attention")
        print("Please check the missing files and features above")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
