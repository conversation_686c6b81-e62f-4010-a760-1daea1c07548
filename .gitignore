###########################
#   Node / Next / Frontend
###########################
/frontend/node_modules/
frontend/.next/
frontend/out/
frontend/build/
frontend/coverage/
frontend/.pnp*
npm-debug.log*
yarn-error.log*
pnpm-debug.log*
frontend/.parcel-cache/
# Ortam değişkenleri
.env*
###########################
#   Python
###########################
# Sanal ortamlar
venv/
.venv/
env/
# Byte-code
**/__pycache__/
*.py[cod]
# Test verileri
tests/**/test_data/
can_dump_test/
###########################
#   Proje meta / IDE
###########################
.DS_Store
Thumbs.db
.idea/
.vscode/
*.sw?
###########################
#   Veritabanı & dump
###########################
*.sqlite3
*.db
*.dump
###########################
#   Diğer
###########################
*.log
references/
.env
can_dump_test