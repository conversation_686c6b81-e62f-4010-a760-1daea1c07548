#!/usr/bin/env python3
"""
Complete Frontend Test Script
Tests all frontend components and pages to ensure they work with backend
"""

import asyncio
import subprocess
import time
import requests
import json
from pathlib import Path

class FrontendTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.backend_process = None
        self.frontend_process = None
        
    async def start_backend(self):
        """Start the FastAPI backend"""
        print("🚀 Starting FastAPI backend...")
        try:
            self.backend_process = subprocess.Popen(
                ["python", "-m", "uvicorn", "app.main:app", "--reload", "--port", "8000"],
                cwd=".",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for backend to start
            for i in range(30):
                try:
                    response = requests.get(f"{self.backend_url}/")
                    if response.status_code == 200:
                        print("✅ Backend started successfully")
                        return True
                except:
                    time.sleep(1)
            
            print("❌ Backend failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting backend: {e}")
            return False
    
    async def start_frontend(self):
        """Start the Next.js frontend"""
        print("🚀 Starting Next.js frontend...")
        try:
            # First install dependencies
            install_process = subprocess.run(
                ["npm", "install"],
                cwd="frontend",
                capture_output=True,
                text=True
            )
            
            if install_process.returncode != 0:
                print(f"❌ npm install failed: {install_process.stderr}")
                return False
            
            # Start development server
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd="frontend",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for frontend to start
            for i in range(60):
                try:
                    response = requests.get(self.frontend_url)
                    if response.status_code == 200:
                        print("✅ Frontend started successfully")
                        return True
                except:
                    time.sleep(2)
            
            print("❌ Frontend failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting frontend: {e}")
            return False
    
    def test_backend_endpoints(self):
        """Test critical backend endpoints"""
        print("\n🧪 Testing Backend Endpoints...")
        
        endpoints = [
            ("/", "Root endpoint"),
            ("/api/v1/status", "Status endpoint"),
            ("/api/v1/frontend/connection/ports", "Available ports"),
            ("/docs", "API documentation"),
        ]
        
        success_count = 0
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}")
                if response.status_code == 200:
                    print(f"✅ {description}: OK")
                    success_count += 1
                else:
                    print(f"❌ {description}: HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ {description}: {e}")
        
        print(f"\n📊 Backend Endpoints: {success_count}/{len(endpoints)} passed")
        return success_count == len(endpoints)
    
    def test_frontend_pages(self):
        """Test frontend pages"""
        print("\n🧪 Testing Frontend Pages...")
        
        pages = [
            ("/", "Home page"),
            ("/dashboard", "Dashboard page"),
            ("/history", "History page"),
            ("/settings", "Settings page"),
            ("/enhanced", "Enhanced features page"),
        ]
        
        success_count = 0
        for page, description in pages:
            try:
                response = requests.get(f"{self.frontend_url}{page}")
                if response.status_code == 200:
                    print(f"✅ {description}: OK")
                    success_count += 1
                else:
                    print(f"❌ {description}: HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ {description}: {e}")
        
        print(f"\n📊 Frontend Pages: {success_count}/{len(pages)} passed")
        return success_count == len(pages)
    
    def test_api_integration(self):
        """Test frontend-backend integration"""
        print("\n🧪 Testing API Integration...")
        
        # Test mock connection
        try:
            response = requests.post(
                f"{self.backend_url}/api/v1/frontend/obd/connect",
                json={
                    "connection_type": "mock",
                    "mock_scenario": "toyota_camry_dtcs"
                }
            )
            
            if response.status_code == 200:
                print("✅ Mock connection: OK")
                
                # Test DTC retrieval
                dtc_response = requests.get(f"{self.backend_url}/api/v1/frontend/dtc/current")
                if dtc_response.status_code == 200:
                    print("✅ DTC retrieval: OK")
                else:
                    print(f"❌ DTC retrieval: HTTP {dtc_response.status_code}")
                
                # Test AI analysis
                ai_response = requests.post(
                    f"{self.backend_url}/api/v1/frontend/dtc/analyze",
                    json={
                        "dtc_codes": ["P0171", "P0300"],
                        "vehicle_info": {"make": "Toyota", "model": "Camry", "year": 2020}
                    }
                )
                
                if ai_response.status_code == 200:
                    print("✅ AI analysis: OK")
                else:
                    print(f"❌ AI analysis: HTTP {ai_response.status_code}")
                
                return True
            else:
                print(f"❌ Mock connection: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API Integration test failed: {e}")
            return False
    
    def check_component_files(self):
        """Check if all component files exist"""
        print("\n🧪 Checking Component Files...")
        
        components = [
            "frontend/src/components/DTCAnalysis.tsx",
            "frontend/src/components/LiveDataDashboard.tsx",
            "frontend/src/components/ExpertModePanel.tsx",
            "frontend/src/components/FreezeFramePanel.tsx",
            "frontend/src/components/ServiceLightReset.tsx",
            "frontend/src/components/ECUInfoPanel.tsx",
            "frontend/src/components/DiagnosticHistory.tsx",
            "frontend/src/components/RealTimeCharts.tsx",
            "frontend/src/components/ConnectionSelector.tsx",
            "frontend/src/components/VehicleInfoPanel.tsx",
            "frontend/src/components/IMReadinessPanel.tsx",
        ]
        
        pages = [
            "frontend/src/app/page.tsx",
            "frontend/src/app/dashboard/page.tsx",
            "frontend/src/app/history/page.tsx",
            "frontend/src/app/settings/page.tsx",
            "frontend/src/app/enhanced/page.tsx",
            "frontend/src/app/layout.tsx",
        ]
        
        all_files = components + pages
        success_count = 0
        
        for file_path in all_files:
            if Path(file_path).exists():
                print(f"✅ {file_path}: EXISTS")
                success_count += 1
            else:
                print(f"❌ {file_path}: MISSING")
        
        print(f"\n📊 Component Files: {success_count}/{len(all_files)} found")
        return success_count == len(all_files)
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🧹 Cleaning up...")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ Backend process terminated")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ Frontend process terminated")
    
    async def run_complete_test(self):
        """Run complete test suite"""
        print("🚀 COMPLETE FRONTEND-BACKEND INTEGRATION TEST")
        print("=" * 60)
        
        try:
            # Check files first
            files_ok = self.check_component_files()
            if not files_ok:
                print("❌ Some component files are missing!")
                return False
            
            # Start backend
            backend_ok = await self.start_backend()
            if not backend_ok:
                return False
            
            # Test backend endpoints
            backend_test_ok = self.test_backend_endpoints()
            
            # Start frontend
            frontend_ok = await self.start_frontend()
            if not frontend_ok:
                return False
            
            # Test frontend pages
            frontend_test_ok = self.test_frontend_pages()
            
            # Test API integration
            integration_ok = self.test_api_integration()
            
            # Summary
            print("\n" + "=" * 60)
            print("📊 TEST SUMMARY")
            print("=" * 60)
            print(f"✅ Component Files: {'PASS' if files_ok else 'FAIL'}")
            print(f"✅ Backend Endpoints: {'PASS' if backend_test_ok else 'FAIL'}")
            print(f"✅ Frontend Pages: {'PASS' if frontend_test_ok else 'FAIL'}")
            print(f"✅ API Integration: {'PASS' if integration_ok else 'FAIL'}")
            
            overall_success = all([files_ok, backend_test_ok, frontend_test_ok, integration_ok])
            print(f"\n🎯 OVERALL RESULT: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
            
            if overall_success:
                print("\n🎉 All tests passed! Frontend is fully integrated with backend.")
                print("🌐 You can now access:")
                print(f"   - Frontend: {self.frontend_url}")
                print(f"   - Backend API: {self.backend_url}/docs")
            
            return overall_success
            
        except KeyboardInterrupt:
            print("\n⚠️ Test interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            return False
        finally:
            self.cleanup()

async def main():
    tester = FrontendTester()
    success = await tester.run_complete_test()
    exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
